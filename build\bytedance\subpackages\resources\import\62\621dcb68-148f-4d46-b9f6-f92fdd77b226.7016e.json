[1, ["d2zwAERaRDL68U/gQX+HEf", "0bcU2J8SNHT4VK5pGgtuvK"], 0, [["cc.TiledMapAsset", ["_name", "tmxXmlStr", "imageLayerTextureNames", "textureNames", "textures", "textureSizes", "imageLayerTextures"], -2, 12, 3]], [[0, 0, 1, 2, 3, 4, 5, 6, 6]], [[0, "ModeManGuardsMap00", "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<map version=\"1.10\" tiledversion=\"1.10.2\" orientation=\"orthogonal\" renderorder=\"right-down\" width=\"75\" height=\"134\" tilewidth=\"10\" tileheight=\"10\" infinite=\"0\" nextlayerid=\"75\" nextobjectid=\"14\">\n <tileset firstgid=\"1\" name=\"scene\" tilewidth=\"755\" tileheight=\"1704\" tilecount=\"1\" columns=\"0\">\n  <grid orientation=\"orthogonal\" width=\"1\" height=\"1\"/>\n  <tile id=\"5\">\n   <image width=\"750\" height=\"1704\" source=\"sceneImg/ModeManGuards/map.png\"/>\n  </tile>\n </tileset>\n <imagelayer id=\"74\" name=\"background\">\n  <image source=\"sceneImg/ModeManGuards/bg2.jpg\" width=\"1125\" height=\"2535\"/>\n </imagelayer>\n <imagelayer id=\"69\" name=\"wall\" offsetx=\"152\" offsety=\"916\">\n  <image source=\"sceneImg/ModeManGuards/img_ydshch_snow.png\" width=\"772\" height=\"712\"/>\n </imagelayer>\n <objectgroup id=\"2\" name=\"born\">\n  <properties>\n   <property name=\"rotateDiretion\" type=\"int\" value=\"1\"/>\n   <property name=\"startAngle\" type=\"int\" value=\"45\"/>\n  </properties>\n  <object id=\"1\" name=\"player\" x=\"527.343\" y=\"12.0923\" width=\"50\" height=\"50\">\n   <properties>\n    <property name=\"rotateDiretion\" type=\"int\" value=\"1\"/>\n    <property name=\"startAngle\" type=\"int\" value=\"15\"/>\n   </properties>\n  </object>\n  <object id=\"3\" name=\"monster_5\" x=\"327.385\" y=\"-84.349\" width=\"50\" height=\"50\">\n   <properties>\n    <property name=\"rotateDiretion\" type=\"int\" value=\"1\"/>\n    <property name=\"startAngle\" type=\"int\" value=\"210\"/>\n   </properties>\n  </object>\n  <object id=\"10\" name=\"monster_1\" x=\"528\" y=\"-197\" width=\"50\" height=\"50\">\n   <properties>\n    <property name=\"rotateDiretion\" type=\"int\" value=\"-1\"/>\n    <property name=\"startAngle\" type=\"int\" value=\"110\"/>\n   </properties>\n  </object>\n  <object id=\"11\" name=\"monster_2\" x=\"728.333\" y=\"-70\" width=\"50\" height=\"50\">\n   <properties>\n    <property name=\"rotateDiretion\" type=\"int\" value=\"1\"/>\n    <property name=\"startAngle\" type=\"int\" value=\"90\"/>\n   </properties>\n  </object>\n  <object id=\"12\" name=\"monster_4\" x=\"375\" y=\"194\" width=\"50\" height=\"50\">\n   <properties>\n    <property name=\"rotateDiretion\" type=\"int\" value=\"-1\"/>\n    <property name=\"startAngle\" type=\"int\" value=\"240\"/>\n   </properties>\n  </object>\n  <object id=\"13\" name=\"monster_3\" x=\"655\" y=\"202\" width=\"50\" height=\"50\">\n   <properties>\n    <property name=\"rotateDiretion\" type=\"int\" value=\"1\"/>\n    <property name=\"startAngle\" type=\"int\" value=\"15\"/>\n   </properties>\n  </object>\n </objectgroup>\n</map>\n", ["sceneImg/ModeManGuards/bg2.jpg", "sceneImg/ModeManGuards/img_ydshch_snow.png"], ["sceneImg/ModeManGuards/map.png"], [null], [[[5, 0, 0]], 8], [0, 1]]], 0, 0, [0, 0], [-1, -2], [0, 1]]