[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R"], ["node", "root", "_spriteFrame", "_parent", "data"], [["cc.Node", ["_name", "_groupIndex", "_active", "_parent", "_prefab", "_components", "_contentSize", "_trs", "_children", "_color", "_eulerAngles", "_anchorPoint"], 0, 1, 4, 9, 5, 7, 2, 5, 5, 5], ["cc.Label", ["_N$verticalAlign", "_N$horizontalAlign", "_fontSize", "_lineHeight", "_string", "_N$overflow", "node", "_materials"], -3, 1, 3], ["cc.Node", ["_name", "_components", "_opacity", "_prefab", "_children", "_contentSize", "_parent"], 0, 4, 12, 5, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Sprite", ["_enabled", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.RigidBody", ["_type", "_gravityScale", "node", "_linearVelocity"], 1, 1, 5], ["cc.PhysicsPolygonCollider", ["tag", "_friction", "_restitution", "_enabled", "node", "points"], -1, 1, 12], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_groupIndex", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 2, 12, 4, 5, 7], ["cc.PhysicsBoxCollider", ["tag", "node", "_offset", "_size"], 2, 1, 5, 5], ["cc.ParticleSystem", ["_custom", "totalParticles", "emissionRate", "startSize", "emitterMode", "speed", "tangentialAccel", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar"], -4, 1, 3, 8, 8, 8, 8], ["cc.PhysicsCircleCollider", ["_friction", "_radius", "node"], 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[3, 0, 1, 2, 2], [4, 2, 3, 1], [0, 0, 3, 5, 4, 6, 7, 2], [5, 0, 2, 2], [0, 0, 1, 3, 5, 4, 6, 3], [0, 0, 1, 3, 5, 4, 7, 3], [8, 0, 1, 2, 3, 4, 5, 6, 7, 3], [5, 1, 2, 3, 2], [6, 0, 1, 2, 4, 5, 4], [9, 0, 1, 2, 3, 2], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 8], [11, 0, 1, 2, 3], [0, 0, 1, 3, 5, 4, 6, 7, 10, 3], [12, 0, 1, 2, 2], [0, 0, 2, 3, 5, 4, 6, 7, 3], [1, 2, 3, 1, 0, 6, 7, 5], [7, 0, 2], [2, 0, 1, 4, 3, 5, 3], [2, 0, 2, 6, 3, 3], [0, 0, 3, 5, 4, 9, 6, 7, 2], [0, 0, 3, 8, 4, 2], [0, 0, 3, 5, 4, 6, 2], [0, 0, 1, 3, 8, 5, 4, 6, 3], [0, 0, 1, 3, 8, 4, 3], [0, 0, 1, 3, 5, 4, 6, 7, 3], [0, 0, 3, 8, 4, 6, 2], [0, 0, 3, 5, 4, 9, 6, 11, 2], [1, 4, 1, 0, 6, 7, 4], [1, 0, 5, 6, 7, 3], [3, 1, 2, 1], [4, 0, 1, 2, 3, 4, 3], [6, 3, 0, 1, 4, 5, 4]], [[16, "zqddn_zhb_level-31864"], [17, "Level", [null], [[-2, -3, -4, -5, [18, "refNode", 83, -7, [0, "19mU8fDSRFh64AICVf/Qq/", -6, 0]]], 1, 1, 1, 1, 4], [29, -1, 0], [5, 750, 1334]], [20, "game", 1, [-8, -9, -10, -11, -12, -13, -14, -15, -16], [0, "f5eE7+6+5GFLPJ1ELq6UPg", 1, 0]], [22, "mapRoot", 8, 2, [-23], [[3, 0, -17], [31, false, 999, 0.5, -18, [[[0, -272.2, -84.8], [0, -236.2, -152.6], [0, -191.9, -205.4], [0, -88.8, -269.1], [0, -14.8, -283.6], [0, -325, -436], [0, -322, 328], [0, 4.46, 296.6], [0, 303, 368], [0, 336, -469], [0, -13.1, -283], [0, 13.5, -282.7], [0, 78.1, -269.6], [0, 157.3, -232.3], [0, 217.8, -174.5], [0, 265.1, -98], [0, 285, -17.3], [0, 284.63, 12.55], [0, 273, 85.1], [0, 219.7, 175], [0, 150.2, 237], [0, 78.1, 269.5], [0, 20.6, 282.6], [0, -16.9, 282.5], [0, -83.4, 265.5], [0, -152.6, 233.5], [0, -225.4, 162], [0, -273.7, 76.6], [0, -285.7, 11.5], [0, -285.2, -12.9]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], [9, 999, -19, [0, 0, 800], [5, 890, 100]], [9, 999, -20, [0, 0, -800], [5, 890, 100]], [9, 999, -21, [0, -500, 0], [5, 100, 1500]], [9, 999, -22, [0, 500, 0], [5, 100, 1500]]], [0, "1eRNpLOCtIZ7BwG8IqHrVD", 1, 0], [5, 560, 560]], [23, "map1", 8, 3, [-24, -25, -26, -27], [0, "fd5fp6MvtO05yMJ7Dr3azs", 1, 0]], [25, "roleRoot", 2, [-28, -29, -30, -31], [0, "debCq0j9hL8oWX2qSX9yad", 1, 0], [5, 62, 113]], [6, "role1", 3, 5, [-34, -35], [[[7, 0, -32, [0, 0, 150]], [11, 0, 20, -33], null], 4, 4, 0], [0, "40ka9pKShGi6+fQbL2Z257", 1, 0], [5, 62, 113], [1.229, 198.706, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "role2", 3, 5, [-38, -39], [[[7, 0, -36, [0, -150, 0]], [11, 0, 20, -37], null], 4, 4, 0], [0, "101QxU4ZJMvarDgk0ODlaZ", 1, 0], [5, 62, 113], [-198.944, -1.386, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "role3", 3, 5, [-42, -43], [[[7, 0, -40, [0, 150, 0]], [11, 0, 20, -41], null], 4, 4, 0], [0, "c6gPMTOPpOh7SSAmBHbrFz", 1, 0], [5, 62, 113], [201.728, -1.056, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "role4", 3, 5, [-46, -47], [[[7, 0, -44, [0, 0, -150]], [11, 0, 20, -45], null], 4, 4, 0], [0, "57t/jh8sdCNqOw0p4352qt", 1, 0], [5, 62, 113], [1.542, -200.971, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "24", 8, 4, [[1, -48, [4]], [3, 0, -49], [8, 1, 0, 1, -50, [[[0, -42, 217], [0, -49, 213], [0, -50, 203], [0, -41, 194], [0, -39, 189], [0, -37, 189], [0, -35, 185], [0, -31, 182], [0, -31, 180], [0, -27, 177], [0, -27, 175], [0, -21, 169], [0, -19, 163], [0, -17, 163], [0, -17, 161], [0, -11, 154], [0, -10, 150], [0, -8, 149], [0, -7, 145], [0, -5, 144], [0, 11, 111], [0, 11, 108], [0, 13, 106], [0, 21, 83], [0, 22, 75], [0, 25, 68], [0, 24, 65], [0, 27, 59], [0, 27, 49], [0, 29, 48], [0, 31, 34], [0, 32, -24], [0, 25, -68], [0, 9, -116], [0, 7, -116], [0, 8, -118], [0, 2, -128], [0, 2, -131], [0, 0, -132], [0, -5, -144], [0, -7, -145], [0, -8, -149], [0, -10, -150], [0, -11, -154], [0, -13, -155], [0, -21, -169], [0, -31, -180], [0, -31, -182], [0, -35, -185], [0, -35, -187], [0, -49, -201], [0, -51, -206], [0, -50, -213], [0, -43, -217], [0, -36, -216], [0, -12, -189], [0, -12, -187], [0, -9, -185], [0, -9, -183], [0, -6, -181], [0, -6, -179], [0, -4, -178], [0, -4, -176], [0, 6, -163], [0, 11, -152], [0, 13, -151], [0, 28, -120], [0, 40, -86], [0, 48, -50], [0, 51, -24], [0, 51, 24], [0, 49, 43], [0, 40, 86], [0, 32, 110], [0, 13, 151], [0, 11, 152], [0, 9, 158], [0, 7, 159], [0, 6, 163], [0, 4, 164], [0, 4, 166], [0, 2, 167], [0, -6, 181], [0, -9, 183], [0, -9, 185], [0, -12, 187], [0, -15, 193], [0, -20, 197], [0, -20, 199], [0, -26, 204], [0, -26, 206], [0, -36, 216]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]], [0, "9c7O1e6f1C6Jjxn9Y2ORpk", 1, 0], [5, 102, 434], [268.04, -0.209, 0, 0, 0, 0, 1, 1, 1, -1]], [12, "24", 8, 4, [[1, -51, [5]], [3, 0, -52], [8, 1, 0, 1, -53, [[[0, -42, 217], [0, -49, 213], [0, -50, 203], [0, -41, 194], [0, -39, 189], [0, -37, 189], [0, -35, 185], [0, -31, 182], [0, -31, 180], [0, -27, 177], [0, -27, 175], [0, -21, 169], [0, -19, 163], [0, -17, 163], [0, -17, 161], [0, -11, 154], [0, -10, 150], [0, -8, 149], [0, -7, 145], [0, -5, 144], [0, 11, 111], [0, 11, 108], [0, 13, 106], [0, 21, 83], [0, 22, 75], [0, 25, 68], [0, 24, 65], [0, 27, 59], [0, 27, 49], [0, 29, 48], [0, 31, 34], [0, 32, -24], [0, 25, -68], [0, 9, -116], [0, 7, -116], [0, 8, -118], [0, 2, -128], [0, 2, -131], [0, 0, -132], [0, -5, -144], [0, -7, -145], [0, -8, -149], [0, -10, -150], [0, -11, -154], [0, -13, -155], [0, -21, -169], [0, -31, -180], [0, -31, -182], [0, -35, -185], [0, -35, -187], [0, -49, -201], [0, -51, -206], [0, -50, -213], [0, -43, -217], [0, -36, -216], [0, -12, -189], [0, -12, -187], [0, -9, -185], [0, -9, -183], [0, -6, -181], [0, -6, -179], [0, -4, -178], [0, -4, -176], [0, 6, -163], [0, 11, -152], [0, 13, -151], [0, 28, -120], [0, 40, -86], [0, 48, -50], [0, 51, -24], [0, 51, 24], [0, 49, 43], [0, 40, 86], [0, 32, 110], [0, 13, 151], [0, 11, 152], [0, 9, 158], [0, 7, 159], [0, 6, 163], [0, 4, 164], [0, 4, 166], [0, 2, 167], [0, -6, 181], [0, -9, 183], [0, -9, 185], [0, -12, 187], [0, -15, 193], [0, -20, 197], [0, -20, 199], [0, -26, 204], [0, -26, 206], [0, -36, 216]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]], [0, "c8mPTPHNVGebHv18bH7Y+v", 1, 0], [5, 102, 434], [0.209, 268.04, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, -1], [1, 0, 0, 90]], [12, "24", 8, 4, [[1, -54, [6]], [3, 0, -55], [8, 1, 0, 1, -56, [[[0, -42, 217], [0, -49, 213], [0, -50, 203], [0, -41, 194], [0, -39, 189], [0, -37, 189], [0, -35, 185], [0, -31, 182], [0, -31, 180], [0, -27, 177], [0, -27, 175], [0, -21, 169], [0, -19, 163], [0, -17, 163], [0, -17, 161], [0, -11, 154], [0, -10, 150], [0, -8, 149], [0, -7, 145], [0, -5, 144], [0, 11, 111], [0, 11, 108], [0, 13, 106], [0, 21, 83], [0, 22, 75], [0, 25, 68], [0, 24, 65], [0, 27, 59], [0, 27, 49], [0, 29, 48], [0, 31, 34], [0, 32, -24], [0, 25, -68], [0, 9, -116], [0, 7, -116], [0, 8, -118], [0, 2, -128], [0, 2, -131], [0, 0, -132], [0, -5, -144], [0, -7, -145], [0, -8, -149], [0, -10, -150], [0, -11, -154], [0, -13, -155], [0, -21, -169], [0, -31, -180], [0, -31, -182], [0, -35, -185], [0, -35, -187], [0, -49, -201], [0, -51, -206], [0, -50, -213], [0, -43, -217], [0, -36, -216], [0, -12, -189], [0, -12, -187], [0, -9, -185], [0, -9, -183], [0, -6, -181], [0, -6, -179], [0, -4, -178], [0, -4, -176], [0, 6, -163], [0, 11, -152], [0, 13, -151], [0, 28, -120], [0, 40, -86], [0, 48, -50], [0, 51, -24], [0, 51, 24], [0, 49, 43], [0, 40, 86], [0, 32, 110], [0, 13, 151], [0, 11, 152], [0, 9, 158], [0, 7, 159], [0, 6, 163], [0, 4, 164], [0, 4, 166], [0, 2, 167], [0, -6, 181], [0, -9, 183], [0, -9, 185], [0, -12, 187], [0, -15, 193], [0, -20, 197], [0, -20, 199], [0, -26, 204], [0, -26, 206], [0, -36, 216]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]], [0, "1d+ks93s5HGr51NiGF5n1e", 1, 0], [5, 102, 434], [-268.04, -0.209, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, -1], [1, 0, 0, 180]], [12, "24", 8, 4, [[1, -57, [7]], [3, 0, -58], [8, 1, 0, 1, -59, [[[0, -42, 217], [0, -49, 213], [0, -50, 203], [0, -41, 194], [0, -39, 189], [0, -37, 189], [0, -35, 185], [0, -31, 182], [0, -31, 180], [0, -27, 177], [0, -27, 175], [0, -21, 169], [0, -19, 163], [0, -17, 163], [0, -17, 161], [0, -11, 154], [0, -10, 150], [0, -8, 149], [0, -7, 145], [0, -5, 144], [0, 11, 111], [0, 11, 108], [0, 13, 106], [0, 21, 83], [0, 22, 75], [0, 25, 68], [0, 24, 65], [0, 27, 59], [0, 27, 49], [0, 29, 48], [0, 31, 34], [0, 32, -24], [0, 25, -68], [0, 9, -116], [0, 7, -116], [0, 8, -118], [0, 2, -128], [0, 2, -131], [0, 0, -132], [0, -5, -144], [0, -7, -145], [0, -8, -149], [0, -10, -150], [0, -11, -154], [0, -13, -155], [0, -21, -169], [0, -31, -180], [0, -31, -182], [0, -35, -185], [0, -35, -187], [0, -49, -201], [0, -51, -206], [0, -50, -213], [0, -43, -217], [0, -36, -216], [0, -12, -189], [0, -12, -187], [0, -9, -185], [0, -9, -183], [0, -6, -181], [0, -6, -179], [0, -4, -178], [0, -4, -176], [0, 6, -163], [0, 11, -152], [0, 13, -151], [0, 28, -120], [0, 40, -86], [0, 48, -50], [0, 51, -24], [0, 51, 24], [0, 49, 43], [0, 40, 86], [0, 32, 110], [0, 13, 151], [0, 11, 152], [0, 9, 158], [0, 7, 159], [0, 6, 163], [0, 4, 164], [0, 4, 166], [0, 2, 167], [0, -6, 181], [0, -9, 183], [0, -9, 185], [0, -12, 187], [0, -15, 193], [0, -20, 197], [0, -20, 199], [0, -26, 204], [0, -26, 206], [0, -36, 216]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]], [0, "56mn95clFJVrOosAxSLdfo", 1, 0], [5, 102, 434], [0.209, -268.04, 0, 0, 0, 0.7071067811865476, -0.7071067811865475, 1, 1, -1], [1, 0, 0, 270]], [2, "New Label", 2, [[27, "目标:", 1, 1, -60, [18]], [13, 3, -61, [4, 4278190080]]], [0, "ffJ/MWy+5CB440sX2juzjK", 1, 0], [5, 97.11, 56.4], [-312.458, 450.506, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "goal", 2, [[28, 1, 1, -62, [19]], [13, 3, -63, [4, 4278190080]]], [0, "de0OW5VTFKWY147Q4pF6Cd", 1, 0], [5, 100, 56.4], [-208.824, 450.506, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "num", 2, [[15, 80, 85, 1, 1, -64, [20]], [13, 3, -65, [4, 4278190080]]], [0, "c8/cSVOYdLprd9XJz+3Y2Z", 1, 0], [5, 6, 113.1], [0, 384.005, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "title", 1, [[15, 45, 55, 1, 1, -66, [0]]], [0, "41LEzKzVBGG5RLz7pgo7P6", 1, 0], [4, 4278190080], [5, 0, 69.3], [0, 435, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "f30972.1=bg", 2, [[1, -67, [1]]], [0, "50A2YIvdhAfpSiWduYEMRT", 1, 0], [5, 750, 1334]], [2, "2=6", 2, [[1, -68, [2]]], [0, "7eKxyNvGNKK4DDSoankJ6h", 1, 0], [5, 40, 40], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "2=7", 2, [[1, -69, [3]]], [0, "5ej6vk97NJ+KSp2Xk9n9MN", 1, 0], [5, 40, 40], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "1", 3, 6, [[1, -70, [8]]], [0, "26cVsaZZxO15nOCKRB3ejT", 1, 0], [5, 40, 40]], [5, "particle1", 3, 6, [[10, true, 100, 20, 40, 1, 18, 0, -71, [9], [4, 4294967295], [4, 0], [4, 16777215], [4, 0]]], [0, "30nK1o0BxOBKlPd7mbIXdY", 1, 0], [0, -2.004, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "2", 3, 7, [[1, -72, [10]]], [0, "deqhsELERP9ZRbzdFPjZJi", 1, 0], [5, 40, 40]], [5, "particle1", 3, 7, [[10, true, 100, 20, 40, 1, 18, 0, -73, [11], [4, 4294967295], [4, 0], [4, 16777215], [4, 0]]], [0, "6353zh4vVDOo2xsYn30A8Z", 1, 0], [0, -2.004, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "3", 3, 8, [[1, -74, [12]]], [0, "1amfXUGDlKkK40pIrXxAXe", 1, 0], [5, 40, 40]], [5, "particle1", 3, 8, [[10, true, 100, 20, 40, 1, 18, 0, -75, [13], [4, 4294967295], [4, 0], [4, 16777215], [4, 0]]], [0, "e0oNbE4cNG3Ji8sWo1HC4O", 1, 0], [0, -2.004, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "4", 3, 9, [[1, -76, [14]]], [0, "8fDWTe/JxNvIGFW96uLH4N", 1, 0], [5, 40, 40]], [5, "particle1", 3, 9, [[10, true, 100, 20, 40, 1, 18, 0, -77, [15], [4, 4294967295], [4, 0], [4, 16777215], [4, 0]]], [0, "ccmIJ2n6tOeZ1PaiEz1M5p", 1, 0], [0, -2.004, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "line", 2, [[30, false, 0, -78, [16], 17]], [0, "5cx8tkVIFC+b4VU+Jzdsod", 1, 0], [4, 4278190080], [5, 200, 10], [0, 0, 0.5]], [14, "cw", false, 1, [[1, -79, [21]]], [0, "0cjIFG+5lKy7WyQkUaN4lG", 1, 0], [5, 114, 114], [25.317, -255.985, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "dg", false, 1, [[1, -80, [22]]], [0, "89gfpKRe9JrJQuejgbFiUH", 1, 0], [5, 145, 112], [0, -265.672, 0, 0, 0, 0, 1, 0.8, 0.8, 1]]], 0, [0, 1, 1, 0, -1, 17, 0, -2, 2, 0, -3, 30, 0, -4, 31, 0, 1, 1, 0, 3, 1, 0, -1, 18, 0, -2, 19, 0, -3, 20, 0, -4, 3, 0, -5, 5, 0, -6, 29, 0, -7, 14, 0, -8, 15, 0, -9, 16, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 4, 0, -1, 10, 0, -2, 11, 0, -3, 12, 0, -4, 13, 0, -1, 6, 0, -2, 7, 0, -3, 8, 0, -4, 9, 0, 0, 6, 0, 0, 6, 0, -1, 21, 0, -2, 22, 0, 0, 7, 0, 0, 7, 0, -1, 23, 0, -2, 24, 0, 0, 8, 0, 0, 8, 0, -1, 25, 0, -2, 26, 0, 0, 9, 0, 0, 9, 0, -1, 27, 0, -2, 28, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 4, 1, 80], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, -1, -1, -1, -1, -1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0]]