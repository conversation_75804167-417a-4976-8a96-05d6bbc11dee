[1, ["ecpdLyjvZBwrvm+cedCcQy", "89YqH0KIZJ26WKtSNfrXcv", "04y3cprWxFQrha4WRCk4AY", "ef1j2vS8ROabD/02fuTola"], ["node", "_spriteFrame", "_N$file", "root", "<PERSON><PERSON><PERSON><PERSON>", "data"], [["cc.Node", ["_name", "_groupIndex", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs"], 1, 9, 4, 5, 2, 1, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["922dfHACR5Gb71WaK/MNbo+", ["node", "_size"], 3, 1, 5], ["cc.RigidBody", ["_type", "node"], 2, 1], ["cc.PhysicsBoxCollider", ["_friction", "node", "_offset", "_size"], 2, 1, 5, 5], ["22ab43vY61En7yPvJO1zuHS", ["node", "<PERSON><PERSON><PERSON><PERSON>"], 3, 1, 1]], [[1, 0, 1, 2, 2], [6, 0, 1, 2, 3, 4, 3], [0, 0, 6, 2, 3, 4, 7, 2], [9, 0, 1, 2, 3, 2], [2, 0, 2], [0, 0, 1, 5, 2, 3, 4, 3], [0, 0, 1, 6, 5, 2, 3, 4, 7, 3], [3, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 5, 6, 6], [5, 0, 1, 2, 2], [1, 1, 2, 1], [7, 0, 1, 1], [8, 0, 1, 2], [10, 0, 1, 1]], [[4, "MPUBEatWall"], [5, "MPUBEatWall", 8, [-7, -8, -9], [[12, 0, -2], [3, 0, -3, [0, -125, 0], [5, 50, 120]], [3, 0, -4, [0, 125, 0], [5, 50, 120]], [13, -6, -5]], [10, -1, 0], [5, 300, 120]], [6, "Eat", 5, 1, [-12], [[1, 1, 0, -10, [1], 2], [11, -11, [5, 200, 60]]], [0, "f1Vk4pHOpHjr9f7173f6sQ", 1, 0], [5, 280, 80], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [7, "label_multiple", 2, [[-13, [9, 3, -14, [4, 4278190080]]], 1, 4], [0, "06/2jfsSBICI4jM9YCpnfz", 1, 0], [5, 116.01, 56.4], [0, 2.905, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "-150", 50, false, 1, 1, 3, [0]], [2, "wall", 1, [[1, 1, 0, -15, [3], 4]], [0, "24c+KvVkdL4bvegRYe3/1M", 1, 0], [5, 40, 120], [-125, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "wall", 1, [[1, 1, 0, -16, [5], 6]], [0, "7cIa/X6YRM1oK6GjxtcPQl", 1, 0], [5, 40, 120], [125, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 4, 4, 0, 0, 1, 0, -1, 2, 0, -2, 5, 0, -3, 6, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -1, 4, 0, 0, 3, 0, 0, 5, 0, 0, 6, 0, 5, 1, 16], [0, 0, 0, 0, 0, 0, 0, 4], [-1, -1, 1, -1, 1, -1, 1, 2], [0, 0, 2, 0, 1, 0, 1, 3]]