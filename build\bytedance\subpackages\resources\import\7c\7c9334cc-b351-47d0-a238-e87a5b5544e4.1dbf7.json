[1, ["ecpdLyjvZBwrvm+cedCcQy", "ef1j2vS8ROabD/02fuTola", "35RFcv50lBVotRgr46UtiC", "c3+ruR7+FEnKfu8yo+WDeT"], ["node", "_N$file", "_spriteFrame", "root", "rightBtn", "leftBtn", "itemIcon", "itemCount", "itemName", "data"], [["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs"], 2, 9, 4, 5, 2, 1, 7], ["cc.Sprite", ["_type", "_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_N$overflow", "_styleFlags", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_children"], 2, 1, 12, 4, 5, 7, 5, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.LabelOutline", ["_width", "_enabled", "node", "_color"], 1, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["50f1deCvjVMZ4CC1qLirWIO", ["node", "itemName", "itemCount", "itemIcon", "leftBtn", "rightBtn"], 3, 1, 1, 1, 1, 1, 1]], [[4, 0, 1, 2, 2], [0, 0, 5, 1, 2, 3, 2], [1, 0, 1, 3, 4, 5, 3], [2, 0, 1, 5, 2, 3, 4, 6, 8, 9, 10, 8], [5, 0, 2, 3, 2], [9, 0, 1, 2, 2], [10, 0, 1, 2, 3, 4], [3, 0, 1, 2, 3, 6, 4, 5, 2], [3, 0, 1, 7, 2, 3, 4, 5, 2], [5, 1, 0, 2, 3, 3], [6, 0, 2], [0, 0, 4, 1, 2, 3, 2], [0, 0, 5, 4, 1, 2, 3, 6, 2], [7, 0, 1, 2, 3, 4, 5, 2], [1, 0, 1, 2, 3, 4, 5, 4], [1, 3, 4, 1], [8, 0, 1, 2, 3, 4], [4, 1, 2, 1], [2, 0, 1, 5, 2, 7, 3, 4, 8, 9, 8], [2, 0, 1, 2, 3, 4, 6, 8, 9, 7], [11, 0, 1, 2, 3, 4, 5, 1]], [[10, "TestItem"], [11, "TestItem", [-8, -9, -10, -11, -12, -13, -14], [[20, -7, -6, -5, -4, -3, -2]], [17, -1, 0], [5, 200, 350]], [8, "item", 1, [-17], [[[2, 1, 0, -15, [6], 7], -16], 4, 1], [0, "0faqBhlZ1PsLXKyeyeS5+9", 1, 0], [5, 48, 68], [-67.934, -25.813, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "item", 1, [-20], [[[2, 1, 0, -18, [11], 12], -19], 4, 1], [0, "49exPbDZRBcqE6scd7H/Sd", 1, 0], [5, 48, 68], [69.082, -25.813, 0, 0, 0, 0, 1, 1, 1, 0]], [12, "item", 1, [-23], [[2, 1, 0, -21, [15], 16], [5, 3, -22, [[6, "50f1deCvjVMZ4CC1qLirWIO", "onBtn", "get", 1]]]], [0, "27IRepg7pKI7yxvqNgU0rX", 1, 0], [5, 140, 68], [2.714, -114.544, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "bg", 1, [[14, 2, 0, false, -24, [0], 1], [16, 45, 116, 120, -25]], [0, "a3lph4JQ9Kc7Wv2gjJpFZT", 1, 0], [5, 200, 350]], [7, "name", 1, [[-26, [9, false, 2, -27, [4, 4278190080]]], 1, 4], [0, "168nscGDFOFLCeNsfJoSlR", 1, 0], [4, 4278190080], [5, 120, 50.4], [0, 49.952, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 2, [[3, "-", 30, false, false, 1, 1, 2, -28, [4], 5], [4, 2, -29, [4, 4278190080]]], [0, "40raJIuBpC/bHQplsnVO5O", 1, 0], [5, 200, 68]], [7, "New Label", 1, [[-30, [9, false, 2, -31, [4, 4278190080]]], 1, 4], [0, "caWQs2JRVDzbgLkDePwJxZ", 1, 0], [4, 4278190080], [5, 100, 68], [0, -25.813, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 3, [[3, "+", 30, false, false, 1, 1, 2, -32, [9], 10], [4, 2, -33, [4, 4278190080]]], [0, "6bqIHrOXZNJ5PUbU+xd+GG", 1, 0], [5, 200, 68]], [1, "New Label", 4, [[3, "获取", 30, false, false, 1, 1, 2, -34, [13], 14], [4, 2, -35, [4, 4278190080]]], [0, "90l0PojF1PIZKAbLbY+Gyn", 1, 0], [5, 200, 68]], [13, "icon", 1, [-36], [0, "c8TQPIne1PvJf+EhL/y/tz", 1, 0], [5, 84, 69], [0, 123.187, 0, 0, 0, 0, 1, 1, 1, 1]], [15, 11, [2]], [18, "生命宝石", 30, false, false, 1, 1, 1, 6, [3]], [5, 3, 2, [[6, "50f1deCvjVMZ4CC1qLirWIO", "onBtn", "minus", 1]]], [19, "1", 30, false, 1, 1, 2, 8, [8]], [5, 3, 3, [[6, "50f1deCvjVMZ4CC1qLirWIO", "onBtn", "plus", 1]]]], 0, [0, 3, 1, 0, 4, 16, 0, 5, 14, 0, 6, 12, 0, 7, 15, 0, 8, 13, 0, 0, 1, 0, -1, 5, 0, -2, 11, 0, -3, 6, 0, -4, 2, 0, -5, 8, 0, -6, 3, 0, -7, 4, 0, 0, 2, 0, -2, 14, 0, -1, 7, 0, 0, 3, 0, -2, 16, 0, -1, 9, 0, 0, 4, 0, 0, 4, 0, -1, 10, 0, 0, 5, 0, 0, 5, 0, -1, 13, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, -1, 15, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, -1, 12, 0, 9, 1, 36], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 15], [-1, 2, -1, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, 1, -1, 2, 1, 1], [0, 3, 0, 0, 0, 1, 0, 2, 0, 0, 1, 0, 2, 0, 1, 0, 2, 1, 1]]