[1, ["29vXfyOiNKfJ2kQ3+RZypx", "54F9Hwcp5MxKvwbJ6CPQ4f"], ["node", "root", "data"], [["cc.CurveRange", ["mode", "constantMax", "constant", "constantMin"], -1], ["cc.Node", ["_name", "_is3DNode", "_prefab", "_parent", "_components", "_trs", "_children", "_anchorPoint", "_eulerAngles"], 1, 4, 1, 9, 7, 2, 5, 5], ["cc.GradientRange", ["_mode", "colorMin", "gradient"], 2, 5, 4], ["cc.<PERSON><PERSON><PERSON>", ["time", "alpha"], 1], ["cc.ParticleSystem3D", ["scaleSpace", "_capacity", "node", "_materials", "startDelay", "startLifetime", "startColor", "startSize", "startSpeed", "startRotation", "gravityModifier", "rateOverTime", "rateOverDistance", "bursts", "_shapeModule", "_colorOverLifetimeModule", "_velocityOvertimeModule", "_limitVelocityOvertimeModule"], 1, 1, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 9, 4, 4, 4, 4], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["time"], 2], ["cc.Prefab", ["_name"], 2], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", ["count"], 3, 4], ["cc.ShapeModule", ["enable", "emitFrom", "radius", "_angle", "arcSpeed", "_scale"], -1, 4, 5], ["cc.ColorOvertimeModule", ["enable", "color"], 2, 4], ["cc.Gradient", ["colorKeys", "alphaKeys"], 3, 9, 9], ["cc.VelocityOvertimeModule", ["enable", "x", "y", "z", "speedModifier"], 2, 4, 4, 4, 4], ["cc.LimitVelocityOvertimeModule", ["enable", "dampen", "limit", "limitX", "limitY", "limitZ"], 1, 4, 4, 4, 4]], [[0, 1], [0, 0, 3, 1, 4], [0, 2, 2], [6, 0, 2], [5, 0, 1, 2, 2], [7, 0, 2], [1, 0, 6, 2, 2], [1, 0, 1, 3, 4, 2, 7, 5, 3], [1, 0, 1, 3, 4, 2, 5, 8, 3], [4, 1, 0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 3], [4, 0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 16, 17, 2], [0, 0, 1, 3], [2, 1], [2, 0, 1, 2], [2, 0, 2, 2], [8, 0, 1], [5, 1, 2, 1], [9, 0, 1, 2, 3, 4, 5, 5], [10, 0, 1, 2], [11, 0, 1, 1], [6, 1], [3, 1], [3, 1, 0, 3], [3, 0, 2], [12, 0, 1, 2, 3, 4, 2], [13, 0, 1, 2, 3, 4, 5, 3]], [[5, "FX_baoxiangda<PERSON>ju"], [6, "FX_baoxiangda<PERSON>ju", [-2, -3], [16, -1, 0]], [7, "gaung<PERSON>hu", true, 1, [[9, 1002, 0, -4, [0], [0], [2, 5], [12], [2, 5], [0], [0], [0], [0], [0], [[15, [2, 1]]]]], [4, "b2crpDDnBB3ZwZlQu1W5oJ", 1, 0], [0, 0, 0.5], [0, 0, 0, 0, 0, 0, 1, 1, 4, 1]], [8, "LIZI", true, 1, [[10, 0, -5, [1], [0], [1, 3, 0.8, 1.5], [13, 2, [4, 4282381311]], [1, 3, 0.2, 0.7], [1, 3, 5, 18], [11, 3, 360], [0], [2, 10], [0], [17, true, 0, 0.6, 0, [0], [1, 1.2, 0, 1]], [18, true, [14, 1, [19, [[20], [3, 0.6409090909090909], [3, 0.6409090909090909], [3, 0.6409090909090909]], [[21], [22, 255, 0.37727272727272726], [23, 0.9954545454545454]]]]], [24, true, [1, 3, 0.3, -0.3], [1, 3, 0.3, -0.3], [1, 3, 0.3, -0.3], [2, 1]], [25, true, 0.2, [0], [0], [0], [0]]]], [4, "achF9QyGxDUL1x5HCgFEr9", 1, 0], [0, -9.717, 0, 0.7071067811865475, 0, 0, 0.7071067811865476, 1, 1, 1], [1, 90, 0, 0]]], 0, [0, 1, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 3, 0, 2, 1, 5], [0, 0], [-1, -1], [0, 1]]