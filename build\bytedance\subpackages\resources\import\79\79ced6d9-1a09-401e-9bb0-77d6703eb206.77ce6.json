[1, ["ecpdLyjvZBwrvm+cedCcQy", "ef1j2vS8ROabD/02fuTola"], ["node", "_N$file", "root", "arrow", "bg", "icon", "label", "data"], [["cc.Node", ["_name", "_children", "_prefab", "_contentSize", "_components", "_trs", "_parent", "_anchorPoint"], 2, 2, 4, 5, 9, 7, 1, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_eulerAngles"], 2, 1, 2, 4, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Sprite", ["node", "_materials"], 3, 1, 3], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -2, 1, 3], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["85f1bxOsk5J140Gtawauuvr", ["node", "label", "icon", "bg", "arrow"], 3, 1, 1, 1, 1, 1]], [[2, 0, 1, 2, 2], [5, 0, 1, 1], [1, 0, 1, 2, 3, 4, 2], [3, 0, 2], [0, 0, 1, 4, 2, 3, 5, 2], [0, 0, 6, 1, 2, 3, 7, 2], [1, 0, 1, 2, 3, 4, 5, 6, 2], [4, 0, 1, 2, 3, 4, 5, 2], [2, 1, 2, 1], [6, 0, 1, 2, 3, 4, 5, 6, 6], [7, 0, 1, 2, 2], [8, 0, 1, 2, 3, 4, 1]], [[3, "TrackItem"], [4, "TrackItem", [-7, -8, -9, -10], [[11, -6, -5, -4, -3, -2]], [8, -1, 0], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 0]], [7, "label", 1, [[-11, [10, 3, -12, [4, 4278190080]]], 1, 4], [0, "e6rh+uhPpH97Cuee8uHuyW", 1, 0], [5, 103.05, 56.4], [0, -53.864, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg", 1, [-13], [0, "0dMPqaLCtBhZjJBWcFBCWt", 1, 0], [5, 90, 90]], [1, 3, [0]], [2, "icon", 1, [-14], [0, "03+bHvcjdEb52Na0mYtK1U", 1, 0], [5, 56, 59]], [1, 5, [1]], [9, "20M", 50, false, 1, 1, 2, [2]], [5, "arrow", 1, [-15], [0, "2aqICZKlVDapxEdXAMPXP7", 1, 0], [5, 100, 120], [0, 0.5, 0.42]], [6, "img", 8, [-16], [0, "324Zm/sGxBwpWoZkeR+9MK", 1, 0], [5, 20, 28], [0, 63.4, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [1, 9, [3]]], 0, [0, 2, 1, 0, 3, 10, 0, 4, 4, 0, 5, 6, 0, 6, 7, 0, 0, 1, 0, -1, 3, 0, -2, 5, 0, -3, 2, 0, -4, 8, 0, -1, 7, 0, 0, 2, 0, -1, 4, 0, -1, 6, 0, -1, 9, 0, -1, 10, 0, 7, 1, 16], [0, 0, 0, 0, 7], [-1, -1, -1, -1, 1], [0, 0, 0, 0, 1]]