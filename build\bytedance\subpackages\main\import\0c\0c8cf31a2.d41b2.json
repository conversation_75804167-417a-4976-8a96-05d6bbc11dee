[1, ["ecpdLyjvZBwrvm+cedCcQy", "d8dty2w0JHtarqLwIM44xG", "b6E6IaPZ5O7Kh3p+asuN9l", "34yVm5rNlGNq8JJW8AYn39", "6azr5kiCVPrqsRmaFz9IGs", "41w4OvVDhPC5TNJKPrgJhI", "4de58wrXhDo4+VUHe+969V"], ["node", "_spriteFrame", "_parent", "_textureSetter", "softICPText", "softRightText", "gamelogo", "progressText", "progress", "scene", "_N$barSprite"], [["cc.Node", ["_name", "_groupIndex", "_id", "_active", "_components", "_trs", "_parent", "_contentSize", "_children", "_anchorPoint", "_color"], -1, 9, 7, 1, 5, 2, 5, 5], ["cc.Widget", ["_alignFlags", "_left", "_right", "_top", "_bottom", "_originalWidth", "_originalHeight", "alignMode", "node", "_target"], -5, 1, 1], ["cc.Node", ["_name", "_groupIndex", "_components", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint", "_color"], 1, 12, 5, 1, 7, 2, 5, 5], ["cc.Sprite", ["_type", "_sizeMode", "_fillRange", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Camera", ["_cullingMask", "_depth", "_clearFlags", "_zoomRatio", "_nearClip", "node", "_backgroundColor"], -2, 1, 5], "cc.SpriteFrame", ["cc.SceneAsset", ["_name", "asyncLoadAssets"], 1], ["cc.Node", ["_name", "_parent", "_components", "_contentSize", "_trs"], 2, 1, 2, 5, 7], ["cc.<PERSON>", ["_fitWidth", "node", "_designResolution"], 2, 1, 5], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Scene", ["_name", "_active", "_children", "_anchorPoint", "_trs"], 1, 2, 5, 7], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_N$fontFamily", "node", "_materials"], -4, 1, 3], ["5b59eOsFG5FwJQQDX+d+/Zn", ["node", "progress", "progressText", "wonderlogo", "gamelogo", "logos", "scenebg", "softRightText", "softICPText"], 3, 1, 1, 1, 1, 1, 3, 1, 1, 1], ["cc.ProgressBar", ["_N$totalLength", "node"], 2, 1]], [[11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 8], [9, 0, 1, 2, 2], [0, 0, 1, 6, 4, 7, 5, 3], [3, 3, 4, 5, 1], [0, 0, 6, 4, 5, 2], [3, 3, 4, 1], [6, 0, 1, 3], [0, 0, 1, 8, 4, 7, 3], [0, 0, 2, 8, 4, 7, 5, 3], [0, 0, 1, 6, 8, 4, 7, 5, 3], [0, 0, 6, 4, 7, 5, 2], [0, 0, 1, 6, 4, 7, 9, 5, 3], [0, 0, 2, 6, 4, 3], [0, 0, 3, 1, 6, 4, 10, 7, 5, 4], [0, 0, 3, 6, 8, 5, 3], [2, 0, 1, 4, 6, 2, 3, 5, 3], [2, 0, 1, 6, 2, 3, 7, 3], [2, 0, 1, 4, 6, 2, 3, 7, 5, 3], [2, 0, 1, 4, 2, 8, 3, 5, 3], [2, 0, 1, 4, 2, 3, 7, 5, 3], [7, 0, 1, 2, 3, 4, 2], [1, 0, 1, 2, 3, 4, 5, 6, 8, 8], [1, 0, 8, 2], [1, 0, 4, 8, 3], [1, 1, 2, 3, 4, 8, 9, 5], [1, 0, 1, 3, 8, 4], [1, 7, 0, 2, 8, 4], [8, 0, 1, 2, 2], [3, 0, 1, 2, 3, 4, 4], [10, 0, 1, 2, 3, 4, 3], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [13, 0, 1, 2], [4, 0, 1, 5, 6, 3], [4, 0, 2, 1, 3, 4, 5, 6]], [[[{"name": "12+", "rect": [0, 0, 122, 156], "offset": [0, 0], "originalSize": [122, 156], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [3], [2]], [[[6, "Launcher", null], [7, "loadingView", 1, [-2, -3, -4, -5, -6, -7], [[21, 45, -245, -245, -183, -183, 1240, 1700, -1]], [5, 1240, 1700]], [8, "<PERSON><PERSON>", "10Lm1h489E/6Z/MWyWcKPj", [-10, -11, 1], [[27, true, -8, [5, 750, 1334]], [22, 45, -9]], [5, 750, 1334], [375, 667, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "tips", 1, 1, [-13, -14, -15, -16], [[23, 4, 199.04999999999995, -12]], [5, 405, 172], [0, -564.95, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "loading", 1, 1, [-19], [[[3, -17, [13], 14], -18], 4, 1], [5, 595, 60], [0, -464.528, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "maskbg", 1, [[24, 535, 215, 1013.25, 320.75, -20, 2], [3, -21, [0], 1]], [5, 640, 1385], [0, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [16, "rz", 1, [-24], [[-22, [1, 2, -23, [4, 4278190080]]], 1, 4], [5, 658.96, 31.72], [0, 0, 0.5]], [17, "bar", 1, 4, [-27], [[-25, [25, 9, 10.757000000000033, 11.384, -26]], 1, 4], [5, 571, 36], [0, 0, 0.5], [-286.74299999999994, 0.6159999999999997, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "New Node", false, [-28, 2], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "loadText", 1, 1, [[-29, [1, 2, -30, [4, 4278190080]]], 1, 4], [4, 4294179838], [5, 70.9, 41.8], [0, -462.614, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "noticle", 1, 3, [[0, "健康游戏忠告", 24, 22, 1, 1, 1, "", -31, [6]], [1, 2, -32, [4, 4278190080]]], [5, 148, 31.72], [0, 46.133, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "noticledesc", 1, 3, [[0, "抵制不良游戏，拒绝盗版游戏，注意自我保护，谨防受骗上当。\n适度游戏益脑，沉迷游戏伤身，合理安排时间，享受健康生活。", 24, 30, 1, 1, 1, "", -33, [7]], [1, 2, -34, [4, 4278190080]]], [5, 676, 71.8], [0, -1.506, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "icp", 1, 6, [[-35, [1, 2, -36, [4, 4278190080]]], 1, 4], [5, 252.2, 31.72], [0, 0, 0.5], [0, -29.027, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "light", 1, 7, [[5, -37, [11]], [26, 2, 32, -19, -38]], [5, 19, 27], [0, 0, 0.5], [571, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "_scriptRoot", "abAorWmCdBbYNpQ1GiU7up", 8, [[30, -44, -43, -42, 3, -41, [15, 16, 17, 18, 19], 5, -40, -39]]], [31, 571, 4], [4, "UICamera", 2, [[32, -1022, 9, -45, [4, 184549376]]], [0, 0, 524.8113946933698, 0, 0, 0, 1, 1, 1, 1]], [4, "GameCamera", 2, [[33, -515, 7, -1, 0.9, 0.1, -46]], [0, 0, 554.2562584220408, 0, 0, 0, 1, 1, 1, 1]], [20, "logo", 1, [-47], [5, 309, 740], [0, 252.428, 0, 0, 0, 0, 1, 1, 1, 1]], [5, 18, [2]], [0, "%d%", 28, 30, 1, 1, 1, "", 9, [3]], [2, "12+", 1, 1, [[3, -48, [4], 5]], [5, 122, 156], [268.994, 543.584, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "gameinfo", false, 1, 3, [[0, "网络游戏出版物号  ISBN978-7-7979-6811-9\n出版单位：北京中科奥科技有限公司\n著作权人：新传在线(北京)信息技术有限公司 登记号：2016SR302017\n审批文号  新广出审[2017]3335号 ", 15, 18, 1, 1, 1, "", -49, [8]]], [4, 4278190080], [5, 463.42, 76.68], [0, -970.275, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "zqxb", false, 3, [6], [-334.804, -49.936, 0, 0, 0, 0, 1, 1, 1, 1]], [0, "琼ICP备2021005596号-18X", 20, 22, 1, 1, 1, "", 12, [9]], [0, "软著权人：广州卓讯互动信息技术有限公司  软著登记号：2021SR1076416", 20, 22, 1, 1, 1, "", 6, [10]], [28, 1, 0, 0.2, 7, [12]]], 0, [0, 0, 1, 0, -1, 5, 0, -2, 18, 0, -3, 4, 0, -4, 9, 0, -5, 21, 0, -6, 3, 0, 0, 2, 0, 0, 2, 0, -1, 16, 0, -2, 17, 0, 0, 3, 0, -1, 10, 0, -2, 11, 0, -3, 22, 0, -4, 23, 0, 0, 4, 0, -2, 15, 0, -1, 7, 0, 0, 5, 0, 0, 5, 0, -1, 25, 0, 0, 6, 0, -1, 12, 0, -1, 26, 0, 0, 7, 0, -1, 13, 0, -1, 14, 0, -1, 20, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, -1, 24, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 4, 24, 0, 5, 25, 0, 6, 19, 0, 7, 20, 0, 8, 15, 0, 0, 14, 0, 0, 16, 0, 0, 17, 0, -1, 19, 0, 0, 21, 0, 0, 22, 0, 9, 8, 1, 2, 2, 2, 2, 8, 6, 2, 23, 15, 10, 26, 49], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 26], [-1, 1, -1, -1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, -2, -3, -4, -5, 1, 1], [0, 3, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 5, 1, 1, 1, 1, 1, 1, 6]]]]