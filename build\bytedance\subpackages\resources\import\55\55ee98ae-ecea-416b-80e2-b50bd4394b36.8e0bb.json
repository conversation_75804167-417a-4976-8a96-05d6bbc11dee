[1, ["7fYbEJjP9DT6Vddf+7HFWk"], 0, [["sp.SkeletonData", ["_name", "_native", "_atlasText", "textureNames", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "shandianbian", ".bin", "\nshandianbian.png\nsize: 1651,132\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nbbbb\n  rotate: false\n  xy: 2, 28\n  size: 607, 102\n  orig: 607, 102\n  offset: 0, 0\n  index: -1\nlll222_00000\n  rotate: false\n  xy: 611, 2\n  size: 128, 128\n  orig: 128, 128\n  offset: 0, 0\n  index: -1\nlll222_00001\n  rotate: false\n  xy: 741, 2\n  size: 128, 128\n  orig: 128, 128\n  offset: 0, 0\n  index: -1\nlll222_00002\n  rotate: false\n  xy: 871, 2\n  size: 128, 128\n  orig: 128, 128\n  offset: 0, 0\n  index: -1\nlll222_00003\n  rotate: false\n  xy: 1001, 2\n  size: 128, 128\n  orig: 128, 128\n  offset: 0, 0\n  index: -1\nlll222_00004\n  rotate: false\n  xy: 1131, 2\n  size: 128, 128\n  orig: 128, 128\n  offset: 0, 0\n  index: -1\nlll222_00005\n  rotate: false\n  xy: 1261, 2\n  size: 128, 128\n  orig: 128, 128\n  offset: 0, 0\n  index: -1\nlll222_00006\n  rotate: false\n  xy: 1391, 2\n  size: 128, 128\n  orig: 128, 128\n  offset: 0, 0\n  index: -1\nlll222_00007\n  rotate: false\n  xy: 1521, 2\n  size: 128, 128\n  orig: 128, 128\n  offset: 0, 0\n  index: -1\n", ["shandianbian.png"], [0]], -1], 0, 0, [0], [-1], [0]]