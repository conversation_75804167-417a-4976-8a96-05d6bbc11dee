[1, ["ecpdLyjvZBwrvm+cedCcQy", "89YqH0KIZJ26WKtSNfrXcv", "ef1j2vS8ROabD/02fuTola", "d7YtQnVZBE5rFPc19m/Ces", "04y3cprWxFQrha4WRCk4AY", "eb9DL+eSlPd7ZR6QiZlQpM", "a2MjXRFdtLlYQ5ouAFv/+R", "9cY26KROhOPJckDfCoiAR5", "b4kzUQNQ5ANJgHGz81Nv+w", "b062b4UcNDzL4BXZ+0tCvT", "a3KBGNCUJOJY37lTxHe3Cp"], ["node", "_spriteFrame", "_parent", "_N$file", "root", "<PERSON><PERSON><PERSON><PERSON>", "hand_box", "data"], [["cc.Node", ["_name", "_groupIndex", "_active", "_opacity", "_prefab", "_contentSize", "_parent", "_trs", "_components", "_eulerAngles", "_anchorPoint", "_children", "_color"], -1, 4, 5, 1, 7, 9, 5, 5, 12, 5], ["e4e15lKTddHTL8uXy9TOACk", ["setSetSpringAngle1", "setSetSpringAngle2", "setAngle1", "setAngle2", "setSpring1", "setAngle", "setSetSpringAngle", "setSpring", "setSpring2", "node"], -6, 1], ["cc.Node", ["_name", "_groupIndex", "_active", "_children", "_prefab", "_parent", "_trs", "_components", "_contentSize"], 0, 2, 4, 1, 7, 9, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.PhysicsBoxCollider", ["_friction", "node", "_size", "_offset"], 2, 1, 5, 5], ["cc.PhysicsPolygonCollider", ["_enabled", "_friction", "node", "points"], 1, 1, 12], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials", "_N$file"], -1, 1, 3, 6], ["922dfHACR5Gb71WaK/MNbo+", ["node", "_size", "_offset"], 3, 1, 5, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize"], 2, 1, 12, 4, 5], ["cc.RigidBody", ["_type", "node"], 2, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["22ab43vY61En7yPvJO1zuHS", ["num", "node", "<PERSON><PERSON><PERSON><PERSON>"], 2, 1, 1], ["54b7bzLQztBJIK+IWxIEjMQ", ["moveTime", "firstPos", "afterPos", "node"], 0, 1], ["cc.PhysicsCircleCollider", ["_friction", "_radius", "node"], 1, 1], ["288d5y2JbJMtKRgK6MyDXPB", ["_radius", "node"], 2, 1], ["5a55e3TjupL/I9+DxL+YRF8", ["node"], 3, 1], ["5688fiOKklDfL+crWJqzZ67", ["num", "node", "<PERSON><PERSON><PERSON><PERSON>"], 2, 1, 1], ["04988ueXn9CcY+dvg+aDUGW", ["isUseMPUBGoodsBullets2", "MPUBGoodsBullets2GravityScale", "MPUBGoodsBullets2GeneraTime", "handStyle", "hand_left_x", "hand_right_x", "pipePower", "bulletNum", "node", "hand_box"], -5, 1, 1]], [[4, 0, 1, 2, 2], [8, 0, 1, 1], [4, 0, 1, 2], [0, 0, 1, 6, 8, 4, 5, 7, 3], [3, 1, 0, 2, 3, 4, 3], [0, 0, 1, 6, 8, 4, 5, 7, 9, 3], [11, 0, 1, 2], [1, 5, 6, 2, 3, 4, 0, 1, 9, 8], [0, 0, 6, 8, 4, 5, 7, 2], [2, 0, 1, 5, 3, 7, 4, 8, 6, 3], [1, 7, 4, 8, 0, 1, 9, 6], [1, 5, 6, 4, 8, 0, 1, 9, 7], [12, 0, 1, 2, 2], [2, 0, 1, 3, 7, 4, 8, 6, 3], [5, 1, 2, 1], [7, 0, 1, 2, 3, 4, 5, 6, 5], [8, 0, 2, 1, 1], [6, 0, 2, 3, 2], [2, 0, 5, 3, 7, 4, 8, 6, 2], [5, 0, 1, 3, 2, 2], [0, 0, 2, 1, 6, 8, 4, 5, 7, 9, 4], [0, 0, 2, 1, 6, 8, 4, 5, 7, 4], [2, 0, 5, 3, 4, 2], [2, 0, 3, 4, 2], [3, 0, 2, 3, 4, 2], [14, 0, 1, 2, 3, 4], [2, 0, 2, 5, 3, 4, 6, 3], [10, 0, 1, 2, 3, 4, 2], [3, 2, 3, 4, 1], [5, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 5, 5], [3, 2, 3, 1], [13, 0, 1, 2, 2], [1, 7, 2, 3, 0, 1, 9, 6], [1, 7, 2, 3, 4, 0, 1, 9, 7], [6, 2, 3, 1], [6, 1, 2, 3, 2], [9, 0, 2], [0, 0, 1, 11, 8, 4, 5, 3], [0, 0, 6, 8, 4, 12, 5, 7, 2], [0, 0, 6, 8, 4, 5, 2], [0, 0, 6, 4, 5, 7, 2], [0, 0, 6, 4, 5, 10, 7, 2], [0, 0, 1, 6, 4, 5, 10, 7, 3], [0, 0, 3, 1, 6, 8, 4, 5, 7, 4], [2, 0, 5, 3, 4, 6, 2], [2, 0, 2, 1, 5, 3, 7, 4, 8, 6, 4], [3, 0, 2, 3, 2], [4, 1, 2, 1], [1, 5, 7, 6, 2, 3, 0, 1, 9, 8], [1, 7, 2, 3, 4, 8, 0, 1, 9, 8], [1, 5, 6, 2, 3, 0, 9, 6], [1, 5, 6, 2, 3, 0, 1, 9, 7], [1, 5, 6, 2, 3, 4, 1, 9, 7], [1, 5, 7, 6, 2, 3, 4, 8, 0, 1, 9, 10], [15, 0, 1, 2, 3], [16, 0, 1, 2], [17, 0, 1], [18, 0, 1, 2, 2], [19, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9]], [[37, "map1"], [38, "map1", 2, [[-4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, [41, "role_box", -22, [0, "daT578aVhJrK1ty+kbCUVG", -21, 0], [5, 200, 150], [-277.474, 384.052, 0, 0, 0, 0, 1, 1, 1, 1]], -23, -24, -25, -26, [42, "monster_box", -28, [0, "7caPzGfrJCRKG8RIbhhKvo", -27, 0], [5, 200, 200], [0, 0, 0], [479.879, 285.974, 0, 0, 0, 0, 1, 1, 1, 1]], -29, -30, -31], 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 4, 1, 1, 1], [[59, true, -2.4, 0.3, 2, -300, 300, 600, 500, -3, -2]], [48, -1, 0], [5, 750, 1334]], [9, "pipeline", 8, 1, [-34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49], [[6, 0, -32], [36, 0.1, -33, [[[0, 209.6, -37.5], [0, 271.7, -67.2], [0, 320.8, -112], [0, 331.2, -136.3], [0, 374.3, -136.4], [0, 374, 91.3], [0, 102.3, 91.2], [0, -234.8, 89.7], [0, -209.6, 71], [0, -100, 72], [0, -73, 72], [0, 0.5, 70], [0, 44.7, 66.8], [0, 66.4, 52.6], [0, 74.4, 33.3], [0, 76, 11.1], [0, 76.7, -5], [0, 147.8, -19.7]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]], [0, "b6C/GzKtVOpJa3CN0aAAz0", 1, 0], [5, 300, 100], [0.021, 204.961, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "M31_MoveWall", 8, [-55, -56, -57], [[6, 0, -51], [19, 0, -52, [0, -65, 0], [5, 30, 60]], [19, 0, -53, [0, 65, 0], [5, 30, 60]], [25, 5, 200, -200, -54]], [2, "f43n60hehAgqeKJ30qdulq", -50], [5, 100, 40], [-0.377, 787.946, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "M31_MoveWall", 8, [-63, -64, -65], [[6, 0, -59], [19, 0, -60, [0, -65, 0], [5, 30, 60]], [19, 0, -61, [0, 65, 0], [5, 30, 60]], [25, 5, -200, 200, -62]], [2, "f43n60hehAgqeKJ30qdulq", -58], [5, 100, 40], [-35.735, 837.533, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "M31_MoveWall", 8, [-71, -72, -73], [[6, 0, -67], [19, 0, -68, [0, -65, 0], [5, 30, 60]], [19, 0, -69, [0, 65, 0], [5, 30, 60]], [25, 5, 200, -200, -70]], [2, "f43n60hehAgqeKJ30qdulq", -66], [5, 100, 40], [-35.735, 837.533, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "M31_MoveWall", 8, [-79, -80, -81], [[6, 0, -75], [19, 0, -76, [0, -65, 0], [5, 30, 60]], [19, 0, -77, [0, 65, 0], [5, 30, 60]], [25, 5, -200, 200, -78]], [2, "f43n60hehAgqeKJ30qdulq", -74], [5, 100, 40], [-35.735, 837.533, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "R", 2, [-82, -83, -84, -85, -86, -87, -88, -89, -90, -91, -92, -93, -94, -95, -96, -97], [0, "90Ak3Jh4JCPaxw22XI/Xqb", 1, 0]], [22, "L", 2, [-98, -99, -100, -101, -102, -103, -104, -105, -106, -107, -108, -109, -110, -111, -112], [0, "b4rgnBjC5KM7UeP7QR3iI9", 1, 0]], [46, "MPUBEatWallWeapon", false, 8, 1, [-119, -120, -121, -122], [[6, 0, -114], [58, 80, -116, -115], [35, -117, [[[0, 117.7, -46.2], [0, 124.2, -36.1], [0, 123.3, 41.6], [0, 87.8, 41.2], [0, 86.3, -35.7], [0, 93.3, -47.5], [0, 105.9, -50.5]], 8, 8, 8, 8, 8, 8, 8]], [35, -118, [[[0, -120.5, -37.7], [0, -115.1, -45.9], [0, -104.7, -49.9], [0, -93.9, -48.2], [0, -86.3, -37.9], [0, -86.3, 42.4], [0, -120.5, 42.9]], 8, 8, 8, 8, 8, 8, 8]]], [2, "a5f9FqgAdC6aHSCPve+Xk+", -113], [5, 300, 120], [-210.508, -204.031, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "MPUBEatWall", 8, 1, [-127, -128, -129], [[6, 0, -124], [32, 50, -126, -125]], [2, "f7+CspfV5C/59Asm137mE9", -123], [5, 300, 120], [-207.831, -347.061, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "MPUBEatWall", 8, 1, [-134, -135, -136], [[6, 0, -131], [32, 30, -133, -132]], [2, "56csi53m5L+62MmjS4YgSm", -130], [5, 300, 120], [209.409, -353.316, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "MPUBWeapon", 7, 9, [-143], [[31, -138, [139]], [6, 0, -139], [55, 0.05, 20, -140], [56, 30, -141], [57, -142]], [2, "49bKh3lAZAp5+Yjfgns5s+", -137], [5, 102, 82], [0, 94.47, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [13, "wall", 8, [-148, -149], [[4, 1, 0, -144, [39], 40], [17, false, -145, [[[0, 13.2, -163], [0, 18.3, -154.5], [0, 17.1, 159.6], [0, -17, 158.5], [0, -18.3, -155.2], [0, -9.6, -165.1], [0, 0.1, -168.6]], 8, 8, 8, 8, 8, 8, 8]], [6, 0, -146], [14, -147, [5, 40, 80]]], [0, "f9sK6E4bpCUrecjFwcyGLo", 1, 0], [5, 40, 80], [102.317, 539.628, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "wall", 8, [-154, -155], [[4, 1, 0, -150, [41], 42], [17, false, -151, [[[0, 10.1, -164.7], [0, 17.6, -156.3], [0, 15.1, 155.5], [0, -15.5, 159.2], [0, -18.5, -155.2], [0, -13.6, -164], [0, -1.8, -168.8]], 8, 8, 8, 8, 8, 8, 8]], [6, 0, -152], [14, -153, [5, 40, 80]]], [0, "26sgt8SpdPs5vEzHqyjmMV", 1, 0], [5, 40, 80], [315.933, 539.628, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "wall", 8, [-160, -161], [[4, 1, 0, -156, [47], 48], [17, false, -157, [[[0, 13.2, -163], [0, 18.3, -154.5], [0, 17.1, 159.6], [0, -17, 158.5], [0, -18.3, -155.2], [0, -9.6, -165.1], [0, 0.1, -168.6]], 8, 8, 8, 8, 8, 8, 8]], [6, 0, -158], [14, -159, [5, 40, 80]]], [0, "72SCDWpLtNR7gCYYNvndWR", 1, 0], [5, 40, 80], [-311.53, 539.638, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "wall", 8, [-166, -167], [[4, 1, 0, -162, [49], 50], [17, false, -163, [[[0, 10.1, -164.7], [0, 17.6, -156.3], [0, 15.1, 155.5], [0, -15.5, 159.2], [0, -18.5, -155.2], [0, -13.6, -164], [0, -1.8, -168.8]], 8, 8, 8, 8, 8, 8, 8]], [6, 0, -164], [14, -165, [5, 40, 80]]], [0, "b7yF1Ibq1HuqF3ZAGk8A1T", 1, 0], [5, 40, 80], [-101.53, 539.638, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "wall", 8, [-172, -173], [[4, 1, 0, -168, [67], 68], [17, false, -169, [[[0, 13.2, -163], [0, 18.3, -154.5], [0, 17.1, 159.6], [0, -17, 158.5], [0, -18.3, -155.2], [0, -9.6, -165.1], [0, 0.1, -168.6]], 8, 8, 8, 8, 8, 8, 8]], [6, 0, -170], [14, -171, [5, 40, 180]]], [0, "4aFIEFhalMJInAP8HaquDF", 1, 0], [5, 40, 180], [102.317, 577.549, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "wall", 8, [-178, -179], [[4, 1, 0, -174, [69], 70], [17, false, -175, [[[0, 10.1, -164.7], [0, 17.6, -156.3], [0, 15.1, 155.5], [0, -15.5, 159.2], [0, -18.5, -155.2], [0, -13.6, -164], [0, -1.8, -168.8]], 8, 8, 8, 8, 8, 8, 8]], [6, 0, -176], [14, -177, [5, 40, 180]]], [0, "d3tHT2XkVGz5upkca2dyfb", 1, 0], [5, 40, 180], [315.933, 577.549, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "wall", 8, [-184, -185], [[4, 1, 0, -180, [79], 80], [17, false, -181, [[[0, 13.2, -163], [0, 18.3, -154.5], [0, 17.1, 159.6], [0, -17, 158.5], [0, -18.3, -155.2], [0, -9.6, -165.1], [0, 0.1, -168.6]], 8, 8, 8, 8, 8, 8, 8]], [6, 0, -182], [14, -183, [5, 40, 180]]], [0, "aeAtAL62VK9K2O1v7qTEDE", 1, 0], [5, 40, 180], [-311.53, 573.486, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "wall", 8, [-190, -191], [[4, 1, 0, -186, [81], 82], [17, false, -187, [[[0, 10.1, -164.7], [0, 17.6, -156.3], [0, 15.1, 155.5], [0, -15.5, 159.2], [0, -18.5, -155.2], [0, -13.6, -164], [0, -1.8, -168.8]], 8, 8, 8, 8, 8, 8, 8]], [6, 0, -188], [14, -189, [5, 40, 180]]], [0, "28mpGVUJhMi53zhVYlVBlX", 1, 0], [5, 40, 180], [-101.53, 573.486, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "wall", 8, [-196, -197], [[4, 1, 0, -192, [103], 104], [17, false, -193, [[[0, 13.2, -163], [0, 18.3, -154.5], [0, 17.1, 159.6], [0, -17, 158.5], [0, -18.3, -155.2], [0, -9.6, -165.1], [0, 0.1, -168.6]], 8, 8, 8, 8, 8, 8, 8]], [6, 0, -194], [14, -195, [5, 40, 250]]], [0, "2b14sVyLlOwLpm26F3d4Nf", 1, 0], [5, 40, 250], [-311.53, 600, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "wall", 8, [-202, -203], [[4, 1, 0, -198, [105], 106], [17, false, -199, [[[0, 10.1, -164.7], [0, 17.6, -156.3], [0, 15.1, 155.5], [0, -15.5, 159.2], [0, -18.5, -155.2], [0, -13.6, -164], [0, -1.8, -168.8]], 8, 8, 8, 8, 8, 8, 8]], [6, 0, -200], [14, -201, [5, 40, 250]]], [0, "064luOPz1MCKN+VMKn5+g1", 1, 0], [5, 40, 250], [-101.53, 600, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "wall", 8, [-208, -209], [[4, 1, 0, -204, [119], 120], [17, false, -205, [[[0, 13.2, -163], [0, 18.3, -154.5], [0, 17.1, 159.6], [0, -17, 158.5], [0, -18.3, -155.2], [0, -9.6, -165.1], [0, 0.1, -168.6]], 8, 8, 8, 8, 8, 8, 8]], [6, 0, -206], [14, -207, [5, 40, 250]]], [0, "98/gxLVaBPN6LhAWH9B99B", 1, 0], [5, 40, 250], [102.317, 600, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "wall", 8, [-214, -215], [[4, 1, 0, -210, [121], 122], [17, false, -211, [[[0, 10.1, -164.7], [0, 17.6, -156.3], [0, 15.1, 155.5], [0, -15.5, 159.2], [0, -18.5, -155.2], [0, -13.6, -164], [0, -1.8, -168.8]], 8, 8, 8, 8, 8, 8, 8]], [6, 0, -212], [14, -213, [5, 40, 250]]], [0, "26Wcc2uepJZb+tZqE1bf7g", 1, 0], [5, 40, 250], [315.933, 600, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "L", [-216, -217, -218, 21, 22], [0, "66tfOWLPhKP6lCkebTFeso", 1, 0]], [23, "R", [-219, -220, -221, 23, 24], [0, "f7fhuRK5xJRYchXCFTSStV", 1, 0]], [23, "R", [-222, -223, 17, 18], [0, "89Awm4wk9LXYwczfOJ7yCX", 1, 0]], [23, "L", [-224, -225, 19, 20], [0, "35UEWKL/9GWI6ApqKEKPg2", 1, 0]], [3, "wall", 8, 1, [[24, 0, -226, [11], 12], [6, 0, -227], [29, 0.1, -228, [5, 750, 50]]], [0, "a88NVQgZpMdabylC609PqV", 1, 0], [5, 750, 50], [-0.73, 621.669, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "Eat", 5, 10, [-231], [[4, 1, 0, -229, [14], 15], [1, -230, [5, 200, 60]]], [2, "f1Vk4pHOpHjr9f7173f6sQ", 10], [5, 200, 60], [0, 0, 1000, 0, 0, 0, 1, 1, 1, 1]], [9, "Eat", 5, 11, [-234], [[4, 1, 0, -232, [21], 22], [1, -233, [5, 200, 60]]], [2, "f1Vk4pHOpHjr9f7173f6sQ", 11], [5, 200, 60], [0, 0, 1000, 0, 0, 0, 1, 1, 1, 1]], [9, "2", 5, 3, [-237], [[4, 1, 0, -235, [29], 30], [16, -236, [0, 0, 20], [5, 100, 10]]], [2, "f1Vk4pHOpHjr9f7173f6sQ", 3], [5, 150, 60], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [18, "wall", 3, [-239, -240], [[4, 1, 0, -238, [31], 32]], [2, "24c+KvVkdL4bvegRYe3/1M", 3], [5, 40, 80], [-77.569, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "wall", 3, [-242, -243], [[4, 1, 0, -241, [33], 34]], [2, "7cIa/X6YRM1oK6GjxtcPQl", 3], [5, 40, 80], [82.549, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "walkNode2", false, 1, [-244, -245, 4], [0, "ccJ5rsG/ZLA5G4d0fLjumh", 1, 0], [0, -763.199, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "R", 35, [-246, 13, 14], [0, "e4ahFOBYRJyqgFGPB6Er3k", 1, 0]], [9, "2", 5, 36, [-249], [[4, 1, 0, -247, [37], 38], [16, -248, [0, 0, -20], [5, 200, 10]]], [0, "720R1p3+5PU48fnXI9OeX9", 1, 0], [5, 200, 60], [210.505, 538.207, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "L", 35, [-250, 15, 16], [0, "81c3H/vcBGMKecFkMjKo54", 1, 0]], [9, "2", 5, 38, [-253], [[4, 1, 0, -251, [45], 46], [16, -252, [0, 0, -20], [5, 200, 10]]], [0, "a4Q9GBKYNDBJspyrkGzLyi", 1, 0], [5, 200, 60], [-209.904, 538.207, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "2", 5, 4, [-256], [[4, 1, 0, -254, [53], 54], [16, -255, [0, 0, 20], [5, 100, 10]]], [2, "f1Vk4pHOpHjr9f7173f6sQ", 4], [5, 150, 60], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [18, "wall", 4, [-258, -259], [[4, 1, 0, -257, [55], 56]], [2, "24c+KvVkdL4bvegRYe3/1M", 4], [5, 40, 80], [-77.569, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "wall", 4, [-261, -262], [[4, 1, 0, -260, [57], 58]], [2, "7cIa/X6YRM1oK6GjxtcPQl", 4], [5, 40, 80], [82.549, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "walkNode3", false, 1, [27, 28, 5], [0, "24H5tqlaNAk7HzL0c/gcwN", 1, 0], [0, -763.199, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "2", 5, 27, [-265], [[4, 1, 0, -263, [61], 62], [16, -264, [0, 0, -20], [5, 200, 10]]], [0, "bbp/wrqlpPKJwNlAbcBtBx", 1, 0], [5, 200, 60], [210.505, 604.061, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "2", 5, 27, [-268], [[4, 1, 0, -266, [65], 66], [16, -267, [0, 0, -20], [5, 200, 10]]], [0, "5duW1sv35L2I94oUNhA1Is", 1, 0], [5, 200, 60], [210.505, 538.207, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "2", 5, 28, [-271], [[4, 1, 0, -269, [73], 74], [16, -270, [0, 0, -20], [5, 200, 10]]], [0, "06cMUAdc5D+LtJLG8HbbTx", 1, 0], [5, 200, 60], [-209.904, 604.061, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "2", 5, 28, [-274], [[4, 1, 0, -272, [77], 78], [16, -273, [0, 0, -20], [5, 200, 10]]], [0, "5bNL7zNh9MOJLlJfVmL0qg", 1, 0], [5, 200, 60], [-209.904, 538.207, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "2", 5, 5, [-277], [[4, 1, 0, -275, [85], 86], [16, -276, [0, 0, 20], [5, 100, 10]]], [2, "f1Vk4pHOpHjr9f7173f6sQ", 5], [5, 150, 60], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [18, "wall", 5, [-279, -280], [[4, 1, 0, -278, [87], 88]], [2, "24c+KvVkdL4bvegRYe3/1M", 5], [5, 40, 80], [-77.569, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "wall", 5, [-282, -283], [[4, 1, 0, -281, [89], 90]], [2, "7cIa/X6YRM1oK6GjxtcPQl", 5], [5, 40, 80], [82.549, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "walkNode4", false, 1, [25, 26, 6], [0, "c39IJ4BN5AU43LE89H+8Vh", 1, 0], [0, -763.199, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "2", 5, 25, [-286], [[4, 1, 0, -284, [93], 94], [16, -285, [0, 0, -20], [5, 200, 10]]], [0, "d9c2t3VHlAf4OQaphQjvyG", 1, 0], [5, 200, 60], [-209.904, 604.061, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "3", 5, 25, [-289], [[4, 1, 0, -287, [97], 98], [16, -288, [0, 0, -20], [5, 180, 10]]], [0, "9ewLUD8vZNgKYTfBkALqxc", 1, 0], [5, 200, 60], [-210.718, 671.608, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "2", 5, 25, [-292], [[4, 1, 0, -290, [101], 102], [16, -291, [0, 0, -20], [5, 200, 10]]], [0, "cdqpFAhqxM6aMPuHHPMomY", 1, 0], [5, 200, 60], [-209.904, 538.207, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "2", 5, 26, [-295], [[4, 1, 0, -293, [109], 110], [16, -294, [0, 0, -20], [5, 200, 10]]], [0, "f34WiDDCFH974e+iXRnUZq", 1, 0], [5, 200, 60], [210.505, 604.061, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "2", 5, 26, [-298], [[4, 1, 0, -296, [113], 114], [16, -297, [0, 0, -20], [5, 180, 10]]], [0, "ccK8F1Su5NnqWspOFSPXjP", 1, 0], [5, 200, 60], [209.691, 671.608, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "2", 5, 26, [-301], [[4, 1, 0, -299, [117], 118], [16, -300, [0, 0, -20], [5, 200, 10]]], [0, "09/WSlcPxF74jdH/5rRs/W", 1, 0], [5, 200, 60], [210.505, 538.207, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "2", 5, 6, [-304], [[4, 1, 0, -302, [125], 126], [16, -303, [0, 0, 20], [5, 100, 10]]], [2, "f1Vk4pHOpHjr9f7173f6sQ", 6], [5, 150, 60], [0, -2.3463124999999536, 1000, 0, 0, 0, 1, 1, 1, 1]], [18, "wall", 6, [-306, -307], [[4, 1, 0, -305, [127], 128]], [2, "24c+KvVkdL4bvegRYe3/1M", 6], [5, 40, 80], [-77.569, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "wall", 6, [-309, -310], [[4, 1, 0, -308, [129], 130]], [2, "7cIa/X6YRM1oK6GjxtcPQl", 6], [5, 40, 80], [82.549, -5.052, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "Eat", 5, 9, [-313], [[4, 1, 0, -311, [132], 133], [1, -312, [5, 200, 60]]], [2, "f1Vk4pHOpHjr9f7173f6sQ", 9], [5, 200, 70], [0, 0, 1000, 0, 0, 0, 1, 1, 1, 1]], [44, "wall", 0, 8, 1, [[4, 1, 0, -314, [140], 141], [36, 0.1, -315, [[[0, 60.6, 5], [0, -138.3, 6.3], [0, -161, 14], [0, -179.6, 25.4], [0, -195.1, 51.2], [0, -198.6, 79.1], [0, -240.6, 79.3], [0, -242, -114], [0, -242, -216], [0, -197.4, -216], [0, -195.1, -140.8], [0, -173.6, -109.6], [0, -135.7, -79.8], [0, -88.6, -57.1], [0, -5.4, -30.1], [0, 58.3, -19.8]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], [6, 0, -316]], [0, "2a6wZ/LTdIv7IEIpi+H92u", 1, 0], [5, 100, 10], [-134.456, 214.418, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "scene2", 1, [-318], [[47, 0, -317, [8]]], [0, "89/VaAa4NF9YDLLEw8sPmq", 1, 0], [5, 750, 125], [0, -12.721, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "w", 8, 1, [[6, 0, -319], [14, -320, [5, 200, 2000]]], [0, "6aO2dPJHxE4oyfJXF/y40N", 1, 0], [5, 200, 2000], [477.053, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "w", 8, 1, [[6, 0, -321], [14, -322, [5, 200, 2000]]], [0, "58ijYrHHxGuL1RNywxfp48", 1, 0], [5, 200, 2000], [-477.053, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "w", 8, 1, [[6, 0, -323], [14, -324, [5, 1600, 200]]], [0, "fegieP/oRIx6qy3KKQJ7SH", 1, 0], [5, 1600, 200], [0, 974.565, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "w", 8, 1, [[6, 0, -325], [14, -326, [5, 1600, 200]]], [0, "cf+8HL3AJFzKEkq1bJ9o+G", 1, 0], [5, 1600, 200], [0, -763.199, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "label_multiple", 30, [[-327, [12, 3, -328, [4, 4278190080]]], 1, 4], [2, "06/2jfsSBICI4jM9YCpnfz", 10], [5, 75.88, 56.4]], [27, "label_multiple", 31, [[-329, [12, 3, -330, [4, 4278190080]]], 1, 4], [2, "06/2jfsSBICI4jM9YCpnfz", 11], [5, 75.71, 56.4]], [8, "label_x2", 32, [[15, "x2", false, 1, 1, -331, [27], 28], [12, 3, -332, [4, 4278190080]]], [2, "edgNuAz9lAC7yi3EZrn4OD", 3], [5, 48.23, 56.4], [0, 5.25031, -1000, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 33, [[1, -333, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -334]], [2, "552WdQUh5E26dccLfLj3JK", 3], [5, 20, 5], [10.716, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 33, [[1, -335, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -336]], [2, "f7a6pBcRJP+6C+rT9GoDgZ", 3], [5, 20, 5], [-9.364, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 34, [[1, -337, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -338]], [2, "97KdxG5ZNG2bvEDr+lQXP5", 3], [5, 20, 5], [10.65, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 34, [[1, -339, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -340]], [2, "58OapVrx9EcKUomVQjCAU+", 3], [5, 20, 5], [-9.43, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 37, [[15, "x2", false, 1, 1, -341, [35], 36], [12, 3, -342, [4, 4278190080]]], [0, "adnQTd/OBI0La8qRkrfYga", 1, 0], [5, 48.23, 56.4], [0, 2.904, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 13, [[1, -343, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -344]], [0, "aayPVPCnpJyprFGzJ+jxuh", 1, 0], [5, 20, 5], [10.463, -42.479, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 13, [[1, -345, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -346]], [0, "f2KywNaepHPZtZQ773t2Nx", 1, 0], [5, 20, 5], [-9.617, -42.479, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 14, [[1, -347, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -348]], [0, "c37Qe9B/5ASYZULGbBa+rU", 1, 0], [5, 20, 5], [10.035, -42.479, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 14, [[1, -349, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -350]], [0, "84pFUCWUxA94gZ6MEy8DKR", 1, 0], [5, 20, 5], [-10.045, -42.479, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 39, [[15, "x2", false, 1, 1, -351, [43], 44], [12, 3, -352, [4, 4278190080]]], [0, "c5lGvleNhAy7MmMurM57LJ", 1, 0], [5, 48.23, 56.4], [0, 2.904, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 15, [[1, -353, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -354]], [0, "75Q1H5a/hMdqXq6odtPlPw", 1, 0], [5, 20, 5], [-9.95, -41.779, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 15, [[1, -355, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -356]], [0, "1cQ4hAvNxNAr/Szk/vxQ/E", 1, 0], [5, 20, 5], [10.13, -41.779, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 16, [[1, -357, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -358]], [0, "46lPzC1GRFhreJMXpAWOsZ", 1, 0], [5, 20, 5], [10.243, -41.779, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 16, [[1, -359, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -360]], [0, "28uZDoRT9F14eh8q1+Fwp1", 1, 0], [5, 20, 5], [-9.837, -41.779, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 40, [[15, "x2", false, 1, 1, -361, [51], 52], [12, 3, -362, [4, 4278190080]]], [2, "edgNuAz9lAC7yi3EZrn4OD", 4], [5, 48.23, 56.4], [0, 5.25031, -1000, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 41, [[1, -363, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -364]], [2, "552WdQUh5E26dccLfLj3JK", 4], [5, 20, 5], [10.716, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 41, [[1, -365, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -366]], [2, "f7a6pBcRJP+6C+rT9GoDgZ", 4], [5, 20, 5], [-9.364, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 42, [[1, -367, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -368]], [2, "97KdxG5ZNG2bvEDr+lQXP5", 4], [5, 20, 5], [10.65, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 42, [[1, -369, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -370]], [2, "58OapVrx9EcKUomVQjCAU+", 4], [5, 20, 5], [-9.43, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 44, [[15, "x2", false, 1, 1, -371, [59], 60], [12, 3, -372, [4, 4278190080]]], [0, "22y5seIppH0Ya/qSksXNcX", 1, 0], [5, 48.23, 56.4], [0, 2.904, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 45, [[15, "x2", false, 1, 1, -373, [63], 64], [12, 3, -374, [4, 4278190080]]], [0, "aeKDpINm5M150HZ4Z9YSIm", 1, 0], [5, 48.23, 56.4], [0, 2.904, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 17, [[1, -375, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -376]], [0, "9c7yj5IT9IiruoKV9KTXWe", 1, 0], [5, 20, 5], [10.463, -91.108, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 17, [[1, -377, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -378]], [0, "99gJreaY1OW65/wA+xmvhw", 1, 0], [5, 20, 5], [-9.617, -91.108, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 18, [[1, -379, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -380]], [0, "bcG/QPduBPTaS0mSxbZEc1", 1, 0], [5, 20, 5], [10.035, -91.108, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 18, [[1, -381, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -382]], [0, "70PumS+QVHYarpsxyGZs+7", 1, 0], [5, 20, 5], [-10.045, -91.108, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 46, [[15, "x2", false, 1, 1, -383, [71], 72], [12, 3, -384, [4, 4278190080]]], [0, "b6ry/s8QlJyZmQ5Et5A7AL", 1, 0], [5, 48.23, 56.4], [0, 2.904, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 47, [[15, "x2", false, 1, 1, -385, [75], 76], [12, 3, -386, [4, 4278190080]]], [0, "38sp0OsgdOEKW/f9S/XOg3", 1, 0], [5, 48.23, 56.4], [0, 2.904, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 19, [[1, -387, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -388]], [0, "2fnpFROoRLHrG5NOLGGq0I", 1, 0], [5, 20, 5], [-9.95, -88.059, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 19, [[1, -389, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -390]], [0, "1cCWEDTaZBTK4Wuf9m5xE7", 1, 0], [5, 20, 5], [10.13, -88.059, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 20, [[1, -391, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -392]], [0, "ffpuHaANRNeZLZY2oj6TY6", 1, 0], [5, 20, 5], [10.243, -88.059, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 20, [[1, -393, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -394]], [0, "98pku9kv5KZ7tFCwTB2lRJ", 1, 0], [5, 20, 5], [-9.837, -88.059, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 48, [[15, "x2", false, 1, 1, -395, [83], 84], [12, 3, -396, [4, 4278190080]]], [2, "edgNuAz9lAC7yi3EZrn4OD", 5], [5, 48.23, 56.4], [0, 5.25031, -1000, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 49, [[1, -397, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -398]], [2, "552WdQUh5E26dccLfLj3JK", 5], [5, 20, 5], [10.716, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 49, [[1, -399, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -400]], [2, "f7a6pBcRJP+6C+rT9GoDgZ", 5], [5, 20, 5], [-9.364, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 50, [[1, -401, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -402]], [2, "97KdxG5ZNG2bvEDr+lQXP5", 5], [5, 20, 5], [10.65, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 50, [[1, -403, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -404]], [2, "58OapVrx9EcKUomVQjCAU+", 5], [5, 20, 5], [-9.43, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 52, [[15, "x2", false, 1, 1, -405, [91], 92], [12, 3, -406, [4, 4278190080]]], [0, "27Tlx1CrtIHJQOHuWDj0mR", 1, 0], [5, 48.23, 56.4], [0, 2.904, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 53, [[15, "x3", false, 1, 1, -407, [95], 96], [12, 3, -408, [4, 4278190080]]], [0, "26O2p04x1LcIhNQF6a/MEY", 1, 0], [5, 48.64, 56.4], [0, 2.904, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 54, [[15, "x2", false, 1, 1, -409, [99], 100], [12, 3, -410, [4, 4278190080]]], [0, "10tKdm781B959fUBxS2K91", 1, 0], [5, 48.23, 56.4], [0, 2.904, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 21, [[1, -411, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -412]], [0, "d5PQl8QaxJl5ESVIL54dOr", 1, 0], [5, 20, 5], [-9.95, -124.945, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 21, [[1, -413, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -414]], [0, "62cMQhY5lFK42257gTR4+T", 1, 0], [5, 20, 5], [10.13, -124.945, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 22, [[1, -415, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -416]], [0, "317lTWbeNEUJb1cDPk2u1s", 1, 0], [5, 20, 5], [10.243, -124.945, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 22, [[1, -417, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -418]], [0, "71hGvceYJMa4YNblM9bQ22", 1, 0], [5, 20, 5], [-9.837, -124.945, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 55, [[15, "x2", false, 1, 1, -419, [107], 108], [12, 3, -420, [4, 4278190080]]], [0, "98iZ7oldNIKJa/WFGYONXo", 1, 0], [5, 48.23, 56.4], [0, 2.904, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 56, [[15, "x2", false, 1, 1, -421, [111], 112], [12, 3, -422, [4, 4278190080]]], [0, "38YSYminVF8I1K/x7p1fBc", 1, 0], [5, 48.23, 56.4], [0, 2.904, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 57, [[15, "x2", false, 1, 1, -423, [115], 116], [12, 3, -424, [4, 4278190080]]], [0, "40L7d2wIFJX6FGPjNZymFn", 1, 0], [5, 48.23, 56.4], [0, 2.904, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 23, [[1, -425, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -426]], [0, "2cwxZzBbROVJrnaMyhRx6s", 1, 0], [5, 20, 5], [10.463, -127, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 23, [[1, -427, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -428]], [0, "afgf5kSvBARYkc2uAJJNnW", 1, 0], [5, 20, 5], [-9.617, -127, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 24, [[1, -429, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -430]], [0, "9bbuV7TzFOJ4OS5c9f/RNZ", 1, 0], [5, 20, 5], [10.035, -127, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 24, [[1, -431, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -432]], [0, "f44La6/NFKlqDwkd89/JRb", 1, 0], [5, 20, 5], [-10.045, -127, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label_x2", 58, [[15, "x2", false, 1, 1, -433, [123], 124], [12, 3, -434, [4, 4278190080]]], [2, "edgNuAz9lAC7yi3EZrn4OD", 6], [5, 48.23, 56.4], [0, 5.25031, -1000, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 59, [[1, -435, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -436]], [2, "552WdQUh5E26dccLfLj3JK", 6], [5, 20, 5], [10.716, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 59, [[1, -437, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -438]], [2, "f7a6pBcRJP+6C+rT9GoDgZ", 6], [5, 20, 5], [-9.364, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 60, [[1, -439, [5, 20, 5]], [10, 0, 0.1, 0.1, 270, 270, -440]], [2, "97KdxG5ZNG2bvEDr+lQXP5", 6], [5, 20, 5], [10.65, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "pipe_v_l", 8, 60, [[1, -441, [5, 20, 5]], [11, 45, 45, 0.1, 0.1, 90, 90, -442]], [2, "58OapVrx9EcKUomVQjCAU+", 6], [5, 20, 5], [-9.43, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "label_multiple", 61, [[-443, [12, 3, -444, [4, 4278190080]]], 1, 4], [2, "06/2jfsSBICI4jM9YCpnfz", 9], [5, 75.3, 56.4]], [3, "wall", 8, 1, [[6, 0, -445], [29, 0, -446, [5, 50, 1200]]], [0, "79Evhznb9FfooifRlvFcco", 1, 0], [5, 50, 1200], [376.336, -233.857, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "wall", 8, 1, [[6, 0, -447], [29, 0, -448, [5, 60, 950]]], [0, "30xpxupxJP5JIcP8o//W21", 1, 0], [5, 60, 950], [-378.21, -189.455, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "pipe_v_l", 8, 2, [[1, -449, [5, 160, 30]], [49, 90, 5, 90, 90, 90, 90, 90, -450]], [0, "d2xcrbilxFub4ahL+tfigR", 1, 0], [5, 160, 30], [0, 74.509, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, 180]], [3, "pipe_v_l", 8, 2, [[1, -451, [5, 10, 50]], [33, 0, 90, 110, 90, 110, -452]], [0, "eeRjE8o3hLr4TFNDRbk0cK", 1, 0], [5, 10, 50], [-155.978, 44.022, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "pipe_v_l", 8, 2, [[1, -453, [5, 20, 100]], [50, 0, 350, 360, 0.1, 0.1, 350, 360, -454]], [0, "07sFCRCrZA2auTeI+bqwX3", 1, 0], [5, 20, 100], [-309.98, 54, 0, 0, 0, 0.25881904510252074, 0.9659258262890683, 1, 1, 1], [1, 0, 0, 30]], [3, "pipe_v_l", 8, 2, [[1, -455, [5, 10, 50]], [33, 0, 90, 100, 90, 100, -456]], [0, "9bF0bcJS5HAK+qrzgmp2Hd", 1, 0], [5, 10, 50], [-102.935, 43.033, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "pipe_v_l", false, 8, 2, [[1, -457, [5, 400, 50]], [51, 45, 45, 45, 60, 45, -458]], [0, "ebvbF1L5VKJpC2DZTVok1N", 1, 0], [5, 400, 50], [259.129, -45.635, 0, 0, 0, 0.17364817766693028, -0.984807753012208, 1, 1, 1], [1, 0, 0, 340]], [20, "pipe_v_l", false, 8, 2, [[1, -459, [5, 200, 30]], [34, 0, 290, 300, 0.5, 290, 300, -460]], [0, "d1SRDQfD5GiJ/+xtIO8qR1", 1, 0], [5, 200, 30], [-285.946, -66.714, 0, 0, 0, 0.25881904510252074, 0.9659258262890683, 1, 1, 1], [1, 0, 0, 30]], [21, "pipe_v_l", false, 8, 2, [[1, -461, [5, 200, 30]], [52, 315, 315, 315, 270, 315, 270, -462]], [0, "afKqLPsphMPLZyfHqf9Rtj", 1, 0], [5, 200, 30], [-185.1, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "pipe_v_l", 8, 7, [[1, -463, [5, 20, 20]], [7, 45, 45, 30, 40, 0.5, 30, 40, -464]], [0, "308Gu42BdA3q4ulKOuOWjz", 1, 0], [5, 20, 20], [335.389, -145.847, 0, 0, 0, 0.7071067811865476, -0.7071067811865475, 1, 1, 1], [1, 0, 0, 270]], [5, "pipe_v_l", 8, 7, [[1, -465, [5, 20, 20]], [7, 45, 45, 30, 40, 0.5, 30, 40, -466]], [0, "54cqsKMh1DwaJfxVJdmX1T", 1, 0], [5, 20, 20], [329.871, -123.552, 0, 0, 0, 0.5735764363510459, -0.8191520442889919, 1, 1, 1], [1, 0, 0, 290]], [5, "pipe_v_l", 8, 7, [[1, -467, [5, 20, 20]], [7, 45, 45, 30, 40, 0.5, 30, 40, -468]], [0, "7eqvlQl4RDVJwQ4QDLlyFX", 1, 0], [5, 20, 20], [320.691, -104.055, 0, 0, 0, 0.49999999999999994, -0.8660254037844387, 1, 1, 1], [1, 0, 0, 300]], [5, "pipe_v_l", 8, 7, [[1, -469, [5, 20, 20]], [7, 45, 45, 45, 50, 0.5, 45, 50, -470]], [0, "40610RxQpK3K9jxoW4THNi", 1, 0], [5, 20, 20], [306.957, -88.589, 0, 0, 0, 0.3420201433256689, -0.9396926207859083, 1, 1, 1], [1, 0, 0, 320]], [5, "pipe_v_l", 8, 7, [[1, -471, [5, 20, 20]], [7, 45, 45, 50, 56, 0.5, 50, 56, -472]], [0, "3cI0a1m5xOPYYmPTmStEdr", 1, 0], [5, 20, 20], [291.584, -76.138, 0, 0, 0, 0.3090169943749475, -0.9510565162951535, 1, 1, 1], [1, 0, 0, 324]], [5, "pipe_v_l", 8, 7, [[1, -473, [5, 20, 20]], [53, 45, 45, 50, 58, 0.5, 58, -474]], [0, "52wo83V5ZPeYTM2mYBCiih", 1, 0], [5, 20, 20], [274.378, -64.52, 0, 0, 0, 0.2756373558169992, -0.9612616959383189, 1, 1, 1], [1, 0, 0, 328]], [5, "pipe_v_l", 8, 7, [[1, -475, [5, 20, 20]], [7, 45, 45, 60, 65, 0.5, 60, 65, -476]], [0, "d0l9Pm9NdPtZfVsdCHNi0f", 1, 0], [5, 20, 20], [255.879, -54.632, 0, 0, 0, 0.21643961393810274, -0.9762960071199334, 1, 1, 1], [1, 0, 0, 335]], [5, "pipe_v_l", 8, 7, [[1, -477, [5, 20, 20]], [7, 45, 45, 60, 66, 0.5, 60, 66, -478]], [0, "1fvinjOSBOaLCZe+7Kx/d3", 1, 0], [5, 20, 20], [237.242, -46.279, 0, 0, 0, 0.20791169081775931, -0.9781476007338057, 1, 1, 1], [1, 0, 0, 336]], [5, "pipe_v_l", 8, 7, [[1, -479, [5, 20, 20]], [7, 45, 45, 65, 70, 0.5, 65, 70, -480]], [0, "4de3s63tBPO6eZuMEMrGTs", 1, 0], [5, 20, 20], [217.28, -37.667, 0, 0, 0, 0.17364817766693028, -0.984807753012208, 1, 1, 1], [1, 0, 0, 340]], [5, "pipe_v_l", 8, 7, [[1, -481, [5, 20, 20]], [7, 45, 45, 65, 75, 0.5, 65, 75, -482]], [0, "afpzT3hSJLEICVfOwLs7NK", 1, 0], [5, 20, 20], [197.666, -30.733, 0, 0, 0, 0.13052619222005157, -0.9914448613738104, 1, 1, 1], [1, 0, 0, 345]], [5, "pipe_v_l", 8, 7, [[1, -483, [5, 20, 20]], [7, 45, 45, 70, 75, 0.5, 70, 75, -484]], [0, "e26xFc0/BMuZ79J+46o9Nt", 1, 0], [5, 20, 20], [178.459, -24.692, 0, 0, 0, 0.12186934340514755, -0.992546151641322, 1, 1, 1], [1, 0, 0, 346]], [5, "pipe_v_l", 8, 7, [[1, -485, [5, 20, 20]], [7, 45, 45, 75, 78, 0.5, 75, 78, -486]], [0, "80EQZwaTRNgoEOMWf8zQS3", 1, 0], [5, 20, 20], [158.196, -19.929, 0, 0, 0, 0.10452846326765373, -0.9945218953682733, 1, 1, 1], [1, 0, 0, 348]], [5, "pipe_v_l", 8, 7, [[1, -487, [5, 20, 20]], [7, 45, 45, 75, 80, 0.5, 75, 80, -488]], [0, "9dByK3oT9OQIRDyTjrIbyh", 1, 0], [5, 20, 20], [137.81, -16.188, 0, 0, 0, 0.0871557427476582, -0.9961946980917455, 1, 1, 1], [1, 0, 0, 350]], [5, "pipe_v_l", 8, 7, [[1, -489, [5, 50, 20]], [7, 44.8, 45, 80, 85, 0.5, 80, 85, -490]], [0, "14bRz9TrxFr4vfVRavOydx", 1, 0], [5, 50, 20], [101.901, -9.73, 0, 0, 0, 0.0871557427476582, -0.9961946980917455, 1, 1, 1], [1, 0, 0, 350]], [3, "pipe_v_l", 8, 7, [[1, -491, [5, 5, 20]], [7, 44.8, 45, 45, 60, 0.5, 45, 60, -492]], [0, "80GSOv7WpN1Z0RNL4eG5vC", 1, 0], [5, 5, 20], [73.102, 21.508, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "pipe_v_l", 8, 7, [[1, -493, [5, 5, 45]], [7, 44.8, 45, 45, 60, 0.5, 45, 60, -494]], [0, "27DVFGMA1NuY23jXQwYOS0", 1, 0], [5, 5, 45], [57.905, 48.034, 0, 0, 0, 0.42261826174069944, 0.9063077870366499, 1, 1, 1], [1, 0, 0, 50]], [5, "pipe_v_l", 8, 8, [[1, -495, [5, 20, 20]], [7, 45, 45, 330, 330, 0.5, 330, 330, -496]], [0, "1aPyJeQQVFUI95htSC8opP", 1, 0], [5, 20, 20], [-333.433, -145.847, 0, 0, 0, 0.7071067811865476, -0.7071067811865475, 1, 1, 1], [1, 0, 0, 270]], [5, "pipe_v_l", 8, 8, [[1, -497, [5, 20, 20]], [34, 0, 315, 330, 0.5, 315, 330, -498]], [0, "44kb6sqYxDX6yk6tDp1nhi", 1, 0], [5, 20, 20], [-326.775, -121.772, 0, 0, 0, -0.25881904510252074, 0.9659258262890683, 1, 1, 1], [1, 0, 0, -30]], [5, "pipe_v_l", 8, 8, [[1, -499, [5, 20, 20]], [7, 45, 45, 315, 330, 0.5, 315, 330, -500]], [0, "19k3l20w1FPbOEh85+GsnL", 1, 0], [5, 20, 20], [-314.64, -103.748, 0, 0, 0, 0.3420201433256689, -0.9396926207859083, 1, 1, 1], [1, 0, 0, 320]], [5, "pipe_v_l", 8, 8, [[1, -501, [5, 20, 20]], [7, 45, 45, 305, 315, 0.5, 305, 315, -502]], [0, "5eiaSZJK5Pd7nmbtLM65ba", 1, 0], [5, 20, 20], [-299.617, -88.267, 0, 0, 0, 0.4226182617406995, -0.9063077870366499, 1, 1, 1], [1, 0, 0, 310]], [5, "pipe_v_l", 8, 8, [[1, -503, [5, 20, 20]], [7, 45, 45, 305, 315, 0.5, 305, 315, -504]], [0, "f15+qC3q9KeLl5j76miUKc", 1, 0], [5, 20, 20], [-283.094, -74.849, 0, 0, 0, 0.4617486132350339, -0.8870108331782217, 1, 1, 1], [1, 0, 0, 305]], [5, "pipe_v_l", 8, 8, [[1, -505, [5, 20, 20]], [7, 45, 45, 305, 315, 0.5, 305, 315, -506]], [0, "a8z9gAH0tMwZVVT2LZ2MJo", 1, 0], [5, 20, 20], [-265.344, -63.092, 0, 0, 0, 0.49999999999999994, -0.8660254037844387, 1, 1, 1], [1, 0, 0, 300]], [5, "pipe_v_l", 8, 8, [[1, -507, [5, 20, 20]], [7, 45, 45, 290, 300, 0.5, 290, 300, -508]], [0, "7eXH31u85IRZlnV9ZFCXbU", 1, 0], [5, 20, 20], [-246.988, -53.313, 0, 0, 0, 0.5372996083468238, -0.8433914458128857, 1, 1, 1], [1, 0, 0, 295]], [5, "pipe_v_l", 8, 8, [[1, -509, [5, 20, 20]], [7, 45, 45, 290, 300, 0.5, 290, 300, -510]], [0, "4dWHr8gE9BNLYWAJ/0pM+j", 1, 0], [5, 20, 20], [-227.879, -45.181, 0, 0, 0, 0.5735764363510459, -0.8191520442889919, 1, 1, 1], [1, 0, 0, 290]], [5, "pipe_v_l", 8, 8, [[1, -511, [5, 20, 20]], [7, 45, 45, 290, 300, 0.5, 290, 300, -512]], [0, "49QdXo+D1N0qtriFJPLybe", 1, 0], [5, 20, 20], [-208.85, -38.088, 0, 0, 0, 0.5807029557109399, -0.8141155183563191, 1, 1, 1], [1, 0, 0, 289]], [5, "pipe_v_l", 8, 8, [[1, -513, [5, 20, 20]], [7, 45, 45, 290, 295, 0.5, 290, 295, -514]], [0, "d9msERwMJEQZesQRjrS6qs", 1, 0], [5, 20, 20], [-189.701, -31.449, 0, 0, 0, 0.5877852522924732, -0.8090169943749473, 1, 1, 1], [1, 0, 0, 288]], [5, "pipe_v_l", 8, 8, [[1, -515, [5, 20, 20]], [7, 45, 45, 285, 290, 0.5, 285, 290, -516]], [0, "ca0xrHCaNLeoxypV8bVJwn", 1, 0], [5, 20, 20], [-170.299, -25.271, 0, 0, 0, 0.6087614290087209, -0.793353340291235, 1, 1, 1], [1, 0, 0, 285]], [5, "pipe_v_l", 8, 8, [[1, -517, [5, 20, 20]], [7, 45, 45, 285, 290, 0.5, 285, 290, -518]], [0, "8c1jDqPJ5DvL14+twYNWAi", 1, 0], [5, 20, 20], [-150.209, -20.294, 0, 0, 0, 0.6427876096865395, -0.7660444431189779, 1, 1, 1], [1, 0, 0, 280]], [5, "pipe_v_l", 8, 8, [[1, -519, [5, 20, 20]], [7, 45, 45, 285, 290, 0.5, 285, 290, -520]], [0, "c8WVeUDxdEpa/aBT0iM8KD", 1, 0], [5, 20, 20], [-130.286, -16.627, 0, 0, 0, 0.6427876096865395, -0.7660444431189779, 1, 1, 1], [1, 0, 0, 280]], [3, "pipe_v_l", 8, 8, [[1, -521, [5, 45, 20]], [7, 44.8, 45, 280, 285, 0.5, 280, 285, -522]], [0, "8d1/0AhhZNILL8g9PYZb4n", 1, 0], [5, 45, 20], [-96.4, -14.68, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "pipe_v_l", 8, 8, [[1, -523, [5, 5, 20]], [54, 90, 2, 90, 280, 300, 2, 2, 280, 300, -524]], [0, "23LsnE4qlJer+F0tZwuNjg", 1, 0], [5, 5, 20], [-70.784, 3.657, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, 180]], [39, "New Sprite(Splash)", 1, [[24, 0, -525, [0], 1]], [0, "49sMHKpzhHkr1zzv+gMvgP", 1, 0], [4, 4292122449], [5, 3000, 3000], [0, 374.745, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "scene", 1, [[28, -526, [2], 3]], [0, "0fdUgVo49Bkrx/fyvC5eHR", 1, 0], [5, 751, 742], [0, 345.285, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "scenne", 1, [[28, -527, [4], 5]], [0, "6ddaGAvzRLdKTZwlCnVkYX", 1, 0], [5, 750, 1334], [0, -60.123, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "scene2", 63, [[24, 0, -528, [6], 7]], [0, "507fqq/X1Ctpw8HaZPzzeO", 1, 0], [5, 750, 500], [0, -900.58, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "New Node", 1, [[24, 0, -529, [9], 10]], [0, "61NhlptCJEjaFDf1hj2kya", 1, 0], [5, 600, 20], [6.311, -602.21, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "-50", false, 1, 1, 68, [13]], [8, "wall", 10, [[4, 1, 0, -530, [16], 17]], [2, "24c+KvVkdL4bvegRYe3/1M", 10], [5, 40, 80], [-105.567, -1.052, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "wall", 10, [[4, 1, 0, -531, [18], 19]], [2, "7cIa/X6YRM1oK6GjxtcPQl", 10], [5, 40, 80], [107.605, -1.052, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "-30", false, 1, 1, 69, [20]], [8, "wall", 11, [[4, 1, 0, -532, [23], 24]], [2, "24c+KvVkdL4bvegRYe3/1M", 11], [5, 40, 80], [-105.567, -1.052, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "wall", 11, [[4, 1, 0, -533, [25], 26]], [2, "7cIa/X6YRM1oK6GjxtcPQl", 11], [5, 40, 80], [107.605, -1.052, 0, 0, 0, 0, 1, 1, 1, 1]], [45, "walkNode1", 1, [3], [0, "bf0DquxwJNZLbJM93YfKHw", 1, 0], [0, -763.199, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "-80", false, 1, 1, 126, [131]], [8, "wall", 9, [[4, 1, 0, -534, [134], 135]], [2, "24c+KvVkdL4bvegRYe3/1M", 9], [5, 40, 100], [-105, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "wall", 9, [[4, 1, 0, -535, [136], 137]], [2, "7cIa/X6YRM1oK6GjxtcPQl", 9], [5, 40, 100], [105, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "icon_gatling_02mask", 12, [[31, -536, [138]]], [2, "75HLE+mRBDup/XnLxil0Hj", 12], [5, 130, 110]], [21, "pipe_v_l", false, 8, 2, [[1, -537, [5, 80, 20]]], [0, "e4g6wl+kJHVbYfQlD/XlQ4", 1, 0], [5, 80, 20], [-2.091, 59.686, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "pipe_h_t", false, 8, 2, [[1, -538, [5, 100, 15]]], [0, "74iiLr/shGi4dxe+h/c2OF", 1, 0], [5, 100, 15], [-278.1, 77.305, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "pipe_v_l", false, 8, 2, [[1, -539, [5, 200, 50]]], [0, "a4u7EaPfVMloIl3uYSz56q", 1, 0], [5, 200, 50], [172.899, -32.759, 0, 0, 0, -0.08715574274765817, 0.9961946980917455, 1, 1, 1], [1, 0, 0, -10]], [20, "pipe_v_r", false, 8, 2, [[1, -540, [5, 200, 30]]], [0, "89fY7woF1CpYHni2PVdjl5", 1, 0], [5, 200, 30], [-176.837, -38.729, 0, 0, 0, 0.17364817766693033, 0.984807753012208, 1, 1, 1], [1, 0, 0, 20]], [20, "pipe_v_r", false, 8, 2, [[1, -541, [5, 100, 30]]], [0, "c3wKmjPfxIaZaGK1dW0Wj5", 1, 0], [5, 100, 30], [-289.538, -96.03, 0, 0, 0, 0.3420201433256687, 0.9396926207859084, 1, 1, 1], [1, 0, 0, 40]], [21, "pipe_v_l", false, 8, 2, [[1, -542, [5, 10, 40]]], [0, "8b6TP5xF1H671VVjZYICBl", 1, 0], [5, 10, 40], [-1.366, 43.631, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "pipe_v_l2", false, 8, 2, [[1, -543, [5, 80, 20]]], [0, "10Q+Z4H01KVYI4JhqVXQtI", 1, 0], [5, 80, 20], [-69.41, 71.556, 0, 0, 0, -0.043619387365336, 0.9990482215818578, 1, 1, 1], [1, 0, 0, -5]], [3, "pipe_end", 8, 1, [[1, -544, [5, 100, 40]]], [0, "9eY60rERVEUpGTNsaaFOze", 1, 0], [5, 100, 40], [-278.636, 287.656, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "hand_box", 2, 1, [0, "54ysF2VvtCd5uzaEzSjQDb", 1, 0], [5, 122, 201], [0, 0.9, 0.2], [0, -513.263, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "img_bgup", 1, [[28, -545, [142], 143]], [0, "c2MdoOGiJEfZ1fvPzl8wD+", 1, 0], [5, 750, 955], [0, -184.001, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 6, 191, 0, 0, 1, 0, -1, 167, 0, -2, 168, 0, -3, 169, 0, -4, 63, 0, -5, 171, 0, -6, 29, 0, -7, 64, 0, -8, 65, 0, -9, 66, 0, -10, 67, 0, -11, 10, 0, -12, 11, 0, -13, 178, 0, -14, 35, 0, -15, 43, 0, -16, 51, 0, -17, 9, 0, 4, 1, 0, 2, 1, 0, -19, 127, 0, -20, 128, 0, -21, 2, 0, -22, 190, 0, 4, 1, 0, 2, 1, 0, -24, 191, 0, -25, 62, 0, -26, 192, 0, 0, 2, 0, 0, 2, 0, -1, 129, 0, -2, 183, 0, -3, 130, 0, -4, 131, 0, -5, 184, 0, -6, 185, 0, -7, 186, 0, -8, 187, 0, -9, 132, 0, -10, 188, 0, -11, 189, 0, -12, 133, 0, -13, 134, 0, -14, 135, 0, -15, 7, 0, -16, 8, 0, 4, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 32, 0, -2, 33, 0, -3, 34, 0, 4, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 40, 0, -2, 41, 0, -3, 42, 0, 4, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 48, 0, -2, 49, 0, -3, 50, 0, 4, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 58, 0, -2, 59, 0, -3, 60, 0, -1, 136, 0, -2, 137, 0, -3, 138, 0, -4, 139, 0, -5, 140, 0, -6, 141, 0, -7, 142, 0, -8, 143, 0, -9, 144, 0, -10, 145, 0, -11, 146, 0, -12, 147, 0, -13, 148, 0, -14, 149, 0, -15, 150, 0, -16, 151, 0, -1, 152, 0, -2, 153, 0, -3, 154, 0, -4, 155, 0, -5, 156, 0, -6, 157, 0, -7, 158, 0, -8, 159, 0, -9, 160, 0, -10, 161, 0, -11, 162, 0, -12, 163, 0, -13, 164, 0, -14, 165, 0, -15, 166, 0, 4, 9, 0, 0, 9, 0, 5, 179, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 61, 0, -2, 180, 0, -3, 181, 0, -4, 12, 0, 4, 10, 0, 0, 10, 0, 5, 172, 0, 0, 10, 0, -1, 30, 0, -2, 173, 0, -3, 174, 0, 4, 11, 0, 0, 11, 0, 5, 175, 0, 0, 11, 0, -1, 31, 0, -2, 176, 0, -3, 177, 0, 4, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, -1, 182, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, -1, 76, 0, -2, 77, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, -1, 78, 0, -2, 79, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, -1, 81, 0, -2, 82, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, -1, 83, 0, -2, 84, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, -1, 92, 0, -2, 93, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, -1, 94, 0, -2, 95, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, -1, 98, 0, -2, 99, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, 0, 20, 0, -1, 100, 0, -2, 101, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, -1, 110, 0, -2, 111, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, -1, 112, 0, -2, 113, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, -1, 117, 0, -2, 118, 0, 0, 24, 0, 0, 24, 0, 0, 24, 0, 0, 24, 0, -1, 119, 0, -2, 120, 0, -1, 52, 0, -2, 53, 0, -3, 54, 0, -1, 55, 0, -2, 56, 0, -3, 57, 0, -1, 44, 0, -2, 45, 0, -1, 46, 0, -2, 47, 0, 0, 29, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, -1, 68, 0, 0, 31, 0, 0, 31, 0, -1, 69, 0, 0, 32, 0, 0, 32, 0, -1, 70, 0, 0, 33, 0, -1, 71, 0, -2, 72, 0, 0, 34, 0, -1, 73, 0, -2, 74, 0, -1, 36, 0, -2, 38, 0, -1, 37, 0, 0, 37, 0, 0, 37, 0, -1, 75, 0, -1, 39, 0, 0, 39, 0, 0, 39, 0, -1, 80, 0, 0, 40, 0, 0, 40, 0, -1, 85, 0, 0, 41, 0, -1, 86, 0, -2, 87, 0, 0, 42, 0, -1, 88, 0, -2, 89, 0, 0, 44, 0, 0, 44, 0, -1, 90, 0, 0, 45, 0, 0, 45, 0, -1, 91, 0, 0, 46, 0, 0, 46, 0, -1, 96, 0, 0, 47, 0, 0, 47, 0, -1, 97, 0, 0, 48, 0, 0, 48, 0, -1, 102, 0, 0, 49, 0, -1, 103, 0, -2, 104, 0, 0, 50, 0, -1, 105, 0, -2, 106, 0, 0, 52, 0, 0, 52, 0, -1, 107, 0, 0, 53, 0, 0, 53, 0, -1, 108, 0, 0, 54, 0, 0, 54, 0, -1, 109, 0, 0, 55, 0, 0, 55, 0, -1, 114, 0, 0, 56, 0, 0, 56, 0, -1, 115, 0, 0, 57, 0, 0, 57, 0, -1, 116, 0, 0, 58, 0, 0, 58, 0, -1, 121, 0, 0, 59, 0, -1, 122, 0, -2, 123, 0, 0, 60, 0, -1, 124, 0, -2, 125, 0, 0, 61, 0, 0, 61, 0, -1, 126, 0, 0, 62, 0, 0, 62, 0, 0, 62, 0, 0, 63, 0, -1, 170, 0, 0, 64, 0, 0, 64, 0, 0, 65, 0, 0, 65, 0, 0, 66, 0, 0, 66, 0, 0, 67, 0, 0, 67, 0, -1, 172, 0, 0, 68, 0, -1, 175, 0, 0, 69, 0, 0, 70, 0, 0, 70, 0, 0, 71, 0, 0, 71, 0, 0, 72, 0, 0, 72, 0, 0, 73, 0, 0, 73, 0, 0, 74, 0, 0, 74, 0, 0, 75, 0, 0, 75, 0, 0, 76, 0, 0, 76, 0, 0, 77, 0, 0, 77, 0, 0, 78, 0, 0, 78, 0, 0, 79, 0, 0, 79, 0, 0, 80, 0, 0, 80, 0, 0, 81, 0, 0, 81, 0, 0, 82, 0, 0, 82, 0, 0, 83, 0, 0, 83, 0, 0, 84, 0, 0, 84, 0, 0, 85, 0, 0, 85, 0, 0, 86, 0, 0, 86, 0, 0, 87, 0, 0, 87, 0, 0, 88, 0, 0, 88, 0, 0, 89, 0, 0, 89, 0, 0, 90, 0, 0, 90, 0, 0, 91, 0, 0, 91, 0, 0, 92, 0, 0, 92, 0, 0, 93, 0, 0, 93, 0, 0, 94, 0, 0, 94, 0, 0, 95, 0, 0, 95, 0, 0, 96, 0, 0, 96, 0, 0, 97, 0, 0, 97, 0, 0, 98, 0, 0, 98, 0, 0, 99, 0, 0, 99, 0, 0, 100, 0, 0, 100, 0, 0, 101, 0, 0, 101, 0, 0, 102, 0, 0, 102, 0, 0, 103, 0, 0, 103, 0, 0, 104, 0, 0, 104, 0, 0, 105, 0, 0, 105, 0, 0, 106, 0, 0, 106, 0, 0, 107, 0, 0, 107, 0, 0, 108, 0, 0, 108, 0, 0, 109, 0, 0, 109, 0, 0, 110, 0, 0, 110, 0, 0, 111, 0, 0, 111, 0, 0, 112, 0, 0, 112, 0, 0, 113, 0, 0, 113, 0, 0, 114, 0, 0, 114, 0, 0, 115, 0, 0, 115, 0, 0, 116, 0, 0, 116, 0, 0, 117, 0, 0, 117, 0, 0, 118, 0, 0, 118, 0, 0, 119, 0, 0, 119, 0, 0, 120, 0, 0, 120, 0, 0, 121, 0, 0, 121, 0, 0, 122, 0, 0, 122, 0, 0, 123, 0, 0, 123, 0, 0, 124, 0, 0, 124, 0, 0, 125, 0, 0, 125, 0, -1, 179, 0, 0, 126, 0, 0, 127, 0, 0, 127, 0, 0, 128, 0, 0, 128, 0, 0, 129, 0, 0, 129, 0, 0, 130, 0, 0, 130, 0, 0, 131, 0, 0, 131, 0, 0, 132, 0, 0, 132, 0, 0, 133, 0, 0, 133, 0, 0, 134, 0, 0, 134, 0, 0, 135, 0, 0, 135, 0, 0, 136, 0, 0, 136, 0, 0, 137, 0, 0, 137, 0, 0, 138, 0, 0, 138, 0, 0, 139, 0, 0, 139, 0, 0, 140, 0, 0, 140, 0, 0, 141, 0, 0, 141, 0, 0, 142, 0, 0, 142, 0, 0, 143, 0, 0, 143, 0, 0, 144, 0, 0, 144, 0, 0, 145, 0, 0, 145, 0, 0, 146, 0, 0, 146, 0, 0, 147, 0, 0, 147, 0, 0, 148, 0, 0, 148, 0, 0, 149, 0, 0, 149, 0, 0, 150, 0, 0, 150, 0, 0, 151, 0, 0, 151, 0, 0, 152, 0, 0, 152, 0, 0, 153, 0, 0, 153, 0, 0, 154, 0, 0, 154, 0, 0, 155, 0, 0, 155, 0, 0, 156, 0, 0, 156, 0, 0, 157, 0, 0, 157, 0, 0, 158, 0, 0, 158, 0, 0, 159, 0, 0, 159, 0, 0, 160, 0, 0, 160, 0, 0, 161, 0, 0, 161, 0, 0, 162, 0, 0, 162, 0, 0, 163, 0, 0, 163, 0, 0, 164, 0, 0, 164, 0, 0, 165, 0, 0, 165, 0, 0, 166, 0, 0, 166, 0, 0, 167, 0, 0, 168, 0, 0, 169, 0, 0, 170, 0, 0, 171, 0, 0, 173, 0, 0, 174, 0, 0, 176, 0, 0, 177, 0, 0, 180, 0, 0, 181, 0, 0, 182, 0, 0, 183, 0, 0, 184, 0, 0, 185, 0, 0, 186, 0, 0, 187, 0, 0, 188, 0, 0, 189, 0, 0, 190, 0, 0, 192, 0, 7, 1, 3, 2, 178, 4, 2, 35, 5, 2, 43, 6, 2, 51, 13, 2, 36, 14, 2, 36, 15, 2, 38, 16, 2, 38, 17, 2, 27, 18, 2, 27, 19, 2, 28, 20, 2, 28, 21, 2, 25, 22, 2, 25, 23, 2, 26, 24, 2, 26, 25, 2, 51, 26, 2, 51, 27, 2, 43, 28, 2, 43, 545], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 172, 175, 179], [-1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, 3, 3, 3], [0, 6, 0, 7, 0, 8, 0, 5, 0, 0, 5, 0, 9, 0, 0, 4, 0, 1, 0, 1, 0, 0, 4, 0, 1, 0, 1, 0, 2, 0, 3, 0, 1, 0, 1, 0, 2, 0, 3, 0, 1, 0, 1, 0, 2, 0, 3, 0, 1, 0, 1, 0, 2, 0, 3, 0, 1, 0, 1, 0, 2, 0, 3, 0, 2, 0, 3, 0, 1, 0, 1, 0, 2, 0, 3, 0, 2, 0, 3, 0, 1, 0, 1, 0, 2, 0, 3, 0, 1, 0, 1, 0, 2, 0, 3, 0, 2, 0, 3, 0, 2, 0, 3, 0, 1, 0, 1, 0, 2, 0, 3, 0, 2, 0, 3, 0, 2, 0, 3, 0, 1, 0, 1, 0, 2, 0, 3, 0, 1, 0, 1, 0, 0, 4, 0, 1, 0, 1, 0, 0, 0, 1, 0, 10, 2, 2, 2]]