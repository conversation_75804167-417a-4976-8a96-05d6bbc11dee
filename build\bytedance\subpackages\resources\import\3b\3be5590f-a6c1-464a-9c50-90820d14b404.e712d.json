[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R"], ["node", "_spriteFrame", "root", "data", "_parent"], [["cc.Node", ["_name", "_groupIndex", "_active", "_components", "_prefab", "_parent", "_contentSize", "_trs", "_children", "_eulerAngles", "_color", "_anchorPoint"], 0, 9, 4, 1, 5, 7, 2, 5, 5, 5], ["cc.Label", ["_N$verticalAlign", "_N$horizontalAlign", "_string", "_styleFlags", "_fontSize", "_lineHeight", "_N$overflow", "node", "_materials"], -4, 1, 3], ["cc.Node", ["_name", "_components", "_children", "_prefab", "_contentSize", "_parent"], 1, 2, 4, 5, 1], ["cc.Sprite", ["_enabled", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.RigidBody", ["_type", "_gravityScale", "node"], 1, 1], ["cc.PhysicsPolygonCollider", ["tag", "_friction", "_restitution", "_enabled", "node", "points"], -1, 1, 12], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_groupIndex", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs", "_eulerAngles"], 1, 1, 2, 12, 4, 5, 7, 5], ["cc.PhysicsBoxCollider", ["tag", "node", "_offset", "_size"], 2, 1, 5, 5], ["cc.ParticleSystem", ["_custom", "_stopped", "totalParticles", "emissionRate", "startSize", "emitterMode", "speed", "tangentialAccel", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar"], -5, 1, 3, 8, 8, 8, 8], ["cc.PhysicsCircleCollider", ["_friction", "_radius", "node"], 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[4, 0, 1, 2, 2], [3, 2, 3, 1], [5, 0, 2, 2], [0, 0, 5, 3, 4, 6, 7, 2], [6, 0, 1, 2, 4, 5, 4], [0, 0, 1, 5, 3, 4, 6, 7, 9, 3], [9, 0, 1, 2, 3, 2], [12, 0, 1, 2, 2], [0, 0, 1, 5, 3, 4, 6, 7, 3], [0, 0, 2, 5, 3, 4, 6, 7, 3], [7, 0, 2], [2, 0, 1, 2, 3, 4, 3], [2, 0, 5, 2, 3, 2], [0, 0, 5, 3, 4, 6, 2], [0, 0, 1, 8, 3, 4, 6, 3], [0, 0, 1, 5, 3, 4, 6, 3], [0, 0, 1, 5, 3, 4, 7, 3], [0, 0, 5, 3, 4, 10, 6, 11, 2], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 3], [3, 0, 1, 2, 3, 4, 3], [4, 1, 2, 1], [5, 1, 2, 2], [6, 3, 0, 1, 4, 5, 4], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 9], [11, 0, 1, 2, 3], [1, 2, 3, 1, 0, 7, 8, 5], [1, 0, 6, 7, 8, 3], [1, 4, 5, 1, 0, 7, 8, 5], [1, 2, 4, 5, 3, 1, 0, 7, 8, 7]], [[10, "zqddn_zhb_level-30974"], [11, "Level", [null], [-2, -3, -4, -5], [20, -1, 0], [5, 750, 1334]], [14, "map", 8, [-12, -13, -14, -15, -16, -17], [[2, 0, -6], [22, false, 999, 0, -7, [[[0, 425, 413], [0, -448, 412], [0, -437, -389], [0, -368.1, -22], [0, -370.7, 19.7], [0, -177.7, 344.3], [0, 181.5, 344.8], [0, 367.7, 20.8], [0, 367.2, -20.2], [0, 177.2, -343.5], [0, -175.6, -346.4], [0, -197, -318.2], [0, -365.6, -26.5], [0, -424, -400], [0, 452, -382]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]], [6, 999, -8, [0, 0, 800], [5, 750, 100]], [6, 999, -9, [0, 0, -800], [5, 750, 100]], [6, 999, -10, [0, -550.4, 0], [5, 100, 1334]], [6, 999, -11, [0, 550, 0], [5, 100, 1334]]], [0, "6aBsfHA/hCtb4/biNMASYo", 1, 0], [5, 560, 560]], [12, "game", 1, [-18, -19, -20, 2, -21, -22, -23, -24, -25], [0, "f5eE7+6+5GFLPJ1ELq6UPg", 1, 0]], [18, "role", 4, 3, [-28, -29], [[[21, 0, -26], [24, 0, 30, -27], null], 4, 4, 0], [0, "d5a/ueLRRIKI46n4Ee7MlT", 1, 0], [5, 62, 113], [-100, 167.47, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, 180]], [8, "5", 8, 2, [[1, -30, [3]], [2, 0, -31], [4, 1, 0, 1, -32, [[[0, -174, 12], [0, -181, 5], [0, -181, -5], [0, -174, -12], [0, 174, -12], [0, 181, -5], [0, 181, 5], [0, 174, 12]], 8, 8, 8, 8, 8, 8, 8, 8]]], [0, "d9A/k1TmBIMLK6QS+W7Ps9", 1, 0], [5, 363, 25], [0, -330, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "5", 8, 2, [[1, -33, [4]], [2, 0, -34], [4, 1, 0, 1, -35, [[[0, -174, 12], [0, -181, 5], [0, -181, -5], [0, -174, -12], [0, 174, -12], [0, 181, -5], [0, 181, 5], [0, 174, 12]], 8, 8, 8, 8, 8, 8, 8, 8]]], [0, "30pPKH4lJNIL32zss5oWR6", 1, 0], [5, 363, 25], [0, 330, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "5", 8, 2, [[1, -36, [5]], [2, 0, -37], [4, 1, 0, 1, -38, [[[0, -174, 12], [0, -181, 5], [0, -181, -5], [0, -174, -12], [0, 174, -12], [0, 181, -5], [0, 181, 5], [0, 174, 12]], 8, 8, 8, 8, 8, 8, 8, 8]]], [0, "f7uq5nR8tCgbHPLNWGlPiE", 1, 0], [5, 363, 25], [-270, 160, 0, 0, 0, 0.49999999999999994, 0.8660254037844387, 1, 1, 1], [1, 0, 0, 60]], [5, "5", 8, 2, [[1, -39, [6]], [2, 0, -40], [4, 1, 0, 1, -41, [[[0, -174, 12], [0, -181, 5], [0, -181, -5], [0, -174, -12], [0, 174, -12], [0, 181, -5], [0, 181, 5], [0, 174, 12]], 8, 8, 8, 8, 8, 8, 8, 8]]], [0, "f58pzez7lE/qK6+t/Jp2ol", 1, 0], [5, 363, 25], [-270, -160, 0, 0, 0, -0.49999999999999994, 0.8660254037844387, 1, 1, 1], [1, 0, 0, -60]], [5, "5", 8, 2, [[1, -42, [7]], [2, 0, -43], [4, 1, 0, 1, -44, [[[0, -174, 12], [0, -181, 5], [0, -181, -5], [0, -174, -12], [0, 174, -12], [0, 181, -5], [0, 181, 5], [0, 174, 12]], 8, 8, 8, 8, 8, 8, 8, 8]]], [0, "afD0gCB/pFsryyBDJoSAh8", 1, 0], [5, 363, 25], [270, 160, 0, 0, 0, -0.49999999999999994, 0.8660254037844387, 1, 1, 1], [1, 0, 0, -60]], [5, "5", 8, 2, [[1, -45, [8]], [2, 0, -46], [4, 1, 0, 1, -47, [[[0, -174, 12], [0, -181, 5], [0, -181, -5], [0, -174, -12], [0, 174, -12], [0, 181, -5], [0, 181, 5], [0, 174, 12]], 8, 8, 8, 8, 8, 8, 8, 8]]], [0, "b8CzqB6M1EPZ9cxHiQ0Wrj", 1, 0], [5, 363, 25], [270, -160, 0, 0, 0, 0.49999999999999994, 0.8660254037844387, 1, 1, 1], [1, 0, 0, 60]], [3, "New Label", 3, [[25, "目标:", 1, 1, 1, -48, [13]], [7, 3, -49, [4, 4278190080]]], [0, "ffJ/MWy+5CB440sX2juzjK", 1, 0], [5, 99.32, 56.4], [-312.458, 450.506, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "goal", 3, [[26, 1, 1, -50, [14]], [7, 3, -51, [4, 4278190080]]], [0, "de0OW5VTFKWY147Q4pF6Cd", 1, 0], [5, 100, 56.4], [-208.824, 450.506, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "num", 3, [[27, 80, 85, 1, 1, -52, [15]], [7, 3, -53, [4, 4278190080]]], [0, "c8/cSVOYdLprd9XJz+3Y2Z", 1, 0], [5, 6, 113.1], [0, 384.005, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "1", 3, [[1, -54, [0]]], [0, "50A2YIvdhAfpSiWduYEMRT", 1, 0], [5, 750, 1334]], [3, "2=6", 3, [[1, -55, [1]]], [0, "bf7j2EdZNJZLR+BFDoDMhL", 1, 0], [5, 40, 40], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "2=7", 3, [[1, -56, [2]]], [0, "215V+IeGlJGZCAoY09U1GS", 1, 0], [5, 40, 40], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "2", 4, 4, [[1, -57, [9]]], [0, "14AO9FEixFbIXUdLxlDVv6", 1, 0], [5, 40, 40]], [16, "particle1", 4, 4, [[23, true, false, 100, 20, 40, 1, 18, 0, -58, [10], [4, 4294967295], [4, 0], [4, 16777215], [4, 0]]], [0, "faVfVQuJNJrJg/kVgPsniv", 1, 0], [0, -2.004, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "line", 3, [[19, false, 0, -59, [11], 12]], [0, "5cx8tkVIFC+b4VU+Jzdsod", 1, 0], [4, 4278190080], [5, 200, 10], [0, 0, 0.5]], [9, "cw", false, 1, [[1, -60, [16]]], [0, "0cjIFG+5lKy7WyQkUaN4lG", 1, 0], [5, 114, 114], [25.317, -255.985, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "dg", false, 1, [[1, -61, [17]]], [0, "89gfpKRe9JrJQuejgbFiUH", 1, 0], [5, 145, 112], [0, -265.672, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [3, "title", 1, [[28, "拦住球球", 45, 55, 1, 1, 1, -62, [18]]], [0, "41LEzKzVBGG5RLz7pgo7P6", 1, 0], [5, 180, 69.3], [0, 500, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, -1, 3, 0, -2, 20, 0, -3, 21, 0, -4, 22, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -5, 9, 0, -6, 10, 0, -1, 14, 0, -2, 15, 0, -3, 16, 0, -5, 4, 0, -6, 19, 0, -7, 11, 0, -8, 12, 0, -9, 13, 0, 0, 4, 0, 0, 4, 0, -1, 17, 0, -2, 18, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 3, 1, 2, 4, 3, 62], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, -1, -1, -1, -1, -1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0]]