[1, ["ecpdLyjvZBwrvm+cedCcQy", "76wSFl701Ol4wBNdBuEgT3", "ef1j2vS8ROabD/02fuTola", "56wtB3qqNKs6hH8sDwaqXO", "e3sBShjv1Kf5fhIfpljds2"], ["node", "_spriteFrame", "_N$file", "root", "data"], [["cc.Node", ["_name", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs"], 2, 4, 5, 9, 1, 2, 7], ["cc.Sprite", ["_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_materials", "_N$file"], -3, 1, 3, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$paddingRight", "_N$spacingX", "node", "_layoutSize"], -2, 1, 5], ["428f06OuyxLY7a+TNJsAr7O", ["node"], 3, 1]], [[2, 0, 1, 2, 2], [0, 0, 4, 3, 1, 2, 6, 2], [1, 2, 3, 4, 1], [3, 0, 2], [0, 0, 5, 3, 1, 2, 2], [0, 0, 4, 5, 3, 1, 2, 6, 2], [0, 0, 4, 5, 1, 2, 2], [0, 0, 4, 3, 1, 2, 2], [1, 0, 1, 2, 3, 4, 3], [2, 1, 2, 1], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 7], [5, 0, 1, 2, 2], [6, 0, 1, 2, 3, 4, 5, 6, 6], [7, 0, 1]], [[3, "Item_RewardList"], [4, "Item_RewardList", [-3, -4], [[13, -2]], [9, -1, 0], [5, 200, 200]], [5, "list", 1, [-7], [[8, 1, 0, -5, [4], 5], [12, 1, 1, 20, 20, 20, -6, [5, 120, 99]]], [0, "8bPSa7tvFIU7S1pHKJc65m", 1, 0], [5, 120, 99], [0, 101.547, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "item", 2, [-8, -9], [0, "85D+MzjexH6pieEo4Uhuha", 1, 0], [5, 80, 100]], [1, "num", 3, [[10, "400", 25, false, 1, 1, 2, -10, [2], 3], [11, 2, -11, [4, 4278190080]]], [0, "64Qag/tuNKz7+32F7KO6uH", 1, 0], [5, 86.13, 50.4], [13.385, -22.735, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "icon", 3, [[2, -12, [0], 1]], [0, "fcqEmBu4hNMb9ZhAUr0UlY", 1, 0], [5, 64, 59]], [1, "bg_talk_01", 1, [[2, -13, [6], 7]], [0, "11tHp9SBRB9oUeeDPkYcqf", 1, 0], [5, 22, 13], [0, 49.5, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 2, 0, -2, 6, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -1, 5, 0, -2, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 4, 1, 13], [0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 2, -1, 1, -1, 1], [0, 1, 0, 2, 0, 3, 0, 4]]