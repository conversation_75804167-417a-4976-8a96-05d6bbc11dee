[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2aHrECZ5APKGS/0d2hvT1", "01HsYT/M9Nu4E5k27Gwhqv"], ["_file", "_spriteFrame", "node", "root", "data"], [["cc.Node", ["_name", "_prefab", "_children", "_parent", "_trs", "_contentSize", "_components", "_eulerAngles"], 2, 4, 2, 1, 7, 5, 9, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.ParticleSystem", ["_dstBlendFactor", "_custom", "_stopped", "totalParticles", "emissionRate", "lifeVar", "angle", "angleVar", "startSize", "speed", "speedVar", "tangentialAccel", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file", "_spriteFrame"], -9, 1, 3, 8, 8, 8, 8, 5, 5, 6, 6]], [[1, 0, 1, 2, 2], [0, 0, 3, 6, 1, 4, 2], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 13], [2, 0, 2], [0, 0, 2, 1, 2], [0, 0, 3, 2, 1, 5, 4, 2], [0, 0, 3, 2, 1, 5, 4, 7, 2], [1, 1, 2, 1]], [[3, "BuffGradeParticle_S"], [4, "fbx_bian<PERSON><PERSON>_huang", [-2, -3], [7, -1, 0]], [5, "ver", 1, [-4, -5], [0, "91vTpceilAH4Cl4QrzRwi4", 1, 0], [5, 1, 1], [0, 10, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Particle", 2, [[2, 1, true, false, 100, 39, 0.5, 0, 0, 9, -10, 10, 0, -6, [0], [4, 4284139519], [4, 0], [4, 4284847359], [4, 0], [0, 0, 140], [0, 5, 5], 1, 2]], [0, "b7jCr53tZJnqdhCyxGVhS/", 1, 0], [-114, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Particle", 2, [[2, 1, true, false, 100, 39, 0.5, 0, 0, 9, -10, 10, 0, -7, [3], [4, 4284139519], [4, 0], [4, 4284847359], [4, 0], [0, 0, 140], [0, 5, 5], 4, 5]], [0, "cey0QcfkJNd7JNKtqUtxkQ", 1, 0], [114, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "hor", 1, [-8], [0, "a9smgcIcNO67TZmGnK2svp", 1, 0], [5, 1, 1], [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [1, "New Particle", 5, [[2, 1, true, false, 100, 39, 0.5, 0, 0, 9, -10, 10, 0, -9, [6], [4, 4284139519], [4, 0], [4, 4284847359], [4, 0], [0, 0, 120], [0, 5, 5], 7, 8]], [0, "b2ewcqXINNNr4JZQ4cdTzI", 1, 0], [-114, 5, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, -1, 2, 0, -2, 5, 0, -1, 3, 0, -2, 4, 0, 2, 3, 0, 2, 4, 0, -1, 6, 0, 2, 6, 0, 4, 1, 9], [0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 0, 1, -1, 0, 1, -1, 0, 1], [0, 1, 2, 0, 1, 2, 0, 1, 2]]