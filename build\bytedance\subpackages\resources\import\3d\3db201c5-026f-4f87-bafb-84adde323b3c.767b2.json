[1, ["7a/QZLET9IDreTiBfRn2PD", "7cqKG36UNIzb9/o99yR7Qe", "46a+dINg1AY5sdbbqkbTtr", "ecpdLyjvZBwrvm+cedCcQy", "70aQONh1NMMKeSqlYvJomI"], ["node", "_N$skeletonData", "_spriteFrame", "root", "data"], [["cc.Node", ["_name", "_opacity", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint"], 0, 9, 4, 5, 1, 7, 2, 5], ["cc.Widget", ["_alignFlags", "_bottom", "_top", "node"], 0, 1], ["0987cXsHepO56AENaH0G6AW", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "premultipliedAlpha", "node", "_materials", "_N$skeletonData"], -2, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["36ac1Nmq0RDA4Cdq1A+7+KF", ["clipImgName", "clipImgRef", "frame_time", "wrapMode", "node"], -1, 1], ["cc.Sprite", ["_dstBlendFactor", "_fillType", "_fillRange", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["9d788GOLNtIE6uIjHxLzG7S", ["node"], 3, 1]], [[3, 0, 1, 2, 2], [4, 0, 2], [0, 0, 1, 8, 3, 4, 5, 9, 3], [0, 0, 2, 6, 3, 4, 5, 7, 3], [0, 0, 6, 3, 4, 5, 7, 2], [1, 0, 1, 3, 3], [1, 0, 2, 3, 3], [2, 0, 1, 2, 3, 5, 6, 7, 5], [2, 0, 1, 2, 4, 3, 5, 6, 7, 6], [3, 1, 2, 1], [5, 0, 1, 2, 3, 4, 5], [6, 0, 1, 2, 3, 4, 5, 6, 5], [7, 0, 1]], [[1, "Effect_13040"], [2, "Effect_1040", 218, [-5, -6], [[10, "img/fight/effect/fx_light0", "1-4", 33.2, 2, -2], [11, 772, 2, 0.2, false, -3, [4], 5], [12, -4]], [9, -1, 0], [5, 64, 247], [0, 0.5, 0]], [3, "fx_spiralknife_star", false, 1, [[5, 4, -46, -7], [7, "default", "fx", 0, "fx", -8, [0], 1]], [0, "8cpqfabehHsou9Nd3R/dGk", 1, 0], [5, 92, 92], [0, -9.199999999999996, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [4, "fx_lightning", 1, [[6, 1, -8.693001525878941, -9], [8, "default", "fx", 0, false, "fx", -10, [2], 3]], [0, "1cLXIWiFRLk4OGsb3QbeAQ", 1, 0], [5, 87.99999237060547, 88.00001525878906], [0, 246.89300000000003, 0, 0, 0, 0, 1, 0.2, 0.2, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 4, 1, 10], [0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 2], [0, 1, 0, 2, 3, 4]]