{"version": "1.1.1", "windows": {"main": {"main": true, "url": "app://editor/index.html", "windowType": "dockable", "x": 314, "y": 91, "width": 1661, "height": 1219, "layout": {"type": "dock-h", "children": [{"width": 327, "height": 1102.3333740234375, "type": "dock-v", "children": [{"width": 327, "height": 395.47918701171875, "type": "panel", "active": 0, "children": ["hierarchy"]}, {"width": 327, "height": 703.8541870117188, "type": "panel", "active": 0, "children": ["assets"]}]}, {"width": 920, "height": 1102.3333740234375, "type": "dock-v", "children": [{"width": 920, "height": 731.8854370117188, "type": "panel", "active": 0, "children": ["scene"]}, {"width": 920, "height": 367.44793701171875, "type": "panel", "active": 0, "children": ["console", "timeline", "game-window"]}]}, {"width": 393, "height": 1102.3333740234375, "type": "panel", "active": 0, "children": ["inspector", "cocos-services"]}]}, "panels": ["hierarchy", "assets", "scene", "console", "timeline", "game-window", "inspector", "cocos-services"]}, "window-1753757569565": {}}, "panels": {"builder": {"x": 888, "y": 287, "width": 527, "height": 749}, "project-settings": {"x": 773, "y": 240, "width": 1351, "height": 938}}, "panelLabelWidth": {}}