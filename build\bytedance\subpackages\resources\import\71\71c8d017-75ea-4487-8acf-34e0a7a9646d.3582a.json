[1, ["85rrfOn9pFkrLkpHXgoRNW"], 0, [["sp.SkeletonData", ["_name", "_native", "_atlasText", "textureNames", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "JiGuang_8_htj", ".bin", "\nJiGuang_8_htj.png\nsize: 1470,262\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\ndian\n  rotate: false\n  xy: 2, 4\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\ndian_glow\n  rotate: false\n  xy: 260, 4\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\nglow\n  rotate: false\n  xy: 1292, 132\n  size: 128, 128\n  orig: 128, 128\n  offset: 0, 0\n  index: -1\nguangshu_glow\n  rotate: false\n  xy: 1422, 13\n  size: 42, 123\n  orig: 73, 128\n  offset: 16, 1\n  index: -1\nguangshu_yanse\n  rotate: false\n  xy: 1422, 138\n  size: 46, 122\n  orig: 73, 128\n  offset: 14, 0\n  index: -1\nhuan\n  rotate: false\n  xy: 518, 4\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\nring\n  rotate: false\n  xy: 1292, 2\n  size: 128, 128\n  orig: 128, 128\n  offset: 0, 0\n  index: -1\nringglow\n  rotate: false\n  xy: 776, 4\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\nspark\n  rotate: false\n  xy: 1034, 4\n  size: 256, 256\n  orig: 256, 256\n  offset: 0, 0\n  index: -1\n", ["JiGuang_8_htj.png"], [0]], -1], 0, 0, [0], [-1], [0]]