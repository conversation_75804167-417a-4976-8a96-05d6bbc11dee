[1, ["ecpdLyjvZBwrvm+cedCcQy", "73jGpne/9JUI/171Zur5Pr", "f8npR6F8ZIZoCp3cKXjJQz", "29FYIk+N1GYaeWH/q1NxQO", "ef1j2vS8ROabD/02fuTola", "a2MjXRFdtLlYQ5ouAFv/+R", "97IAXRHnlLq7dNeJPB6SbL", "7d1EjwRqBJh7jheADVzsCF", "4dMZmkcjlJEbhEI0rAtegJ", "c1f4UDWZJNUJmmvN1IN/rK", "f7293wEF9JhIuMEsrLuqng", "72ROiLJR1FabuT1UeEP0hT"], ["node", "_spriteFrame", "_N$file", "_parent", "_N$disabledSprite", "_N$normalSprite", "_N$font", "templete", "root", "DiffSelect", "my<PERSON>rid<PERSON>iew", "content", "data"], [["cc.Node", ["_name", "_opacity", "_active", "_groupIndex", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 5, 9, 1, 7, 2, 5, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_left", "_right", "_top", "_bottom", "node"], -5, 1], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_styleFlags", "_enableWrapText", "_N$cacheMode", "_lineHeight", "_N$overflow", "node", "_materials", "_N$file"], -7, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 1, 1, 12, 4, 5, 7, 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite", "_N$normalSprite", "_N$target"], 1, 1, 9, 5, 5, 6, 6, 1], ["cc.Prefab", ["_name"], 2], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$lineHeight", "node", "_N$font"], -1, 1, 6], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "scrollEvents", "_N$content"], -2, 1, 9, 1], ["9bd0fvydv5K0ZXnwdO5A2Td", ["node", "space", "content"], 3, 1, 5, 1], ["88486GjA5dIGbBp/5ecunKP", ["node", "labelArr", "content", "my<PERSON>rid<PERSON>iew", "DiffSelect"], 3, 1, 2, 1, 1, 1]], [[5, 0, 1, 2, 2], [1, 0, 1, 2, 8, 4], [9, 0, 1, 2, 2], [10, 0, 1, 2, 3], [0, 0, 7, 9, 6, 4, 5, 2], [0, 0, 7, 6, 4, 5, 8, 2], [0, 0, 7, 9, 6, 4, 5, 8, 2], [2, 0, 3, 4, 5, 2], [2, 1, 0, 3, 4, 5, 3], [7, 0, 2], [0, 0, 3, 9, 6, 4, 5, 3], [0, 0, 1, 7, 6, 4, 10, 5, 3], [0, 0, 9, 4, 5, 2], [0, 0, 7, 6, 4, 5, 2], [0, 0, 2, 1, 7, 6, 4, 10, 5, 8, 4], [0, 0, 9, 6, 4, 5, 8, 2], [0, 0, 2, 7, 6, 4, 10, 5, 8, 3], [0, 0, 7, 9, 4, 8, 2], [0, 0, 7, 6, 4, 5, 11, 8, 2], [0, 0, 7, 9, 6, 4, 5, 11, 8, 2], [0, 0, 6, 4, 5, 11, 8, 2], [4, 0, 1, 2, 3, 4, 5, 6, 3], [4, 0, 2, 7, 3, 4, 5, 6, 2], [1, 0, 8, 2], [1, 3, 0, 4, 5, 6, 7, 1, 2, 8, 9], [1, 3, 0, 1, 8, 4], [2, 2, 1, 0, 3, 4, 5, 4], [2, 3, 4, 5, 1], [5, 1, 2, 1], [8, 0, 1], [3, 0, 4, 8, 1, 5, 2, 3, 10, 11, 12, 8], [3, 0, 4, 6, 1, 2, 3, 7, 10, 11, 12, 8], [3, 0, 6, 1, 2, 3, 9, 7, 10, 11, 12, 8], [3, 0, 4, 1, 5, 2, 3, 10, 11, 7], [6, 1, 0, 2, 3, 4, 5, 7, 6, 3], [6, 0, 2, 3, 4, 5, 8, 6, 2], [11, 0, 1, 2, 3, 4, 5, 5], [12, 0, 1, 2, 2], [13, 0, 1, 2, 3, 4, 5, 6, 7, 6], [14, 0, 1, 2, 1], [15, 0, 1, 2, 3, 4, 1]], [[9, "M20_Prepare_Award"], [10, "M20_Prepare_Award", 1, [-7, -8], [[40, -6, [-5], -4, -3, -2]], [28, -1, 0], [5, 750, 1334]], [12, "content", [-9, -10, -11, -12, -13, -14, -15], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 645, 930]], [15, "Background", [-18], [[27, -16, [18], 19], [24, 0, 45, 8, 8, 1.8549999999999995, 13.145, 100, 40, -17]], [0, "adOwuR8ZVAD4UZUBkrmhH9", 1, 0], [5, 64, 65], [0, 5.645, 0, 0, 0, 0, 1, 1, 1, 0]], [20, "content", [[25, 0, 41, 614, -19]], [0, "8a+xXy2ThIVp4va/4sA0ZX", 1, 0], [5, 605, 274], [0, 0, 1], [-302.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "bg", 2, [[8, 1, 0, -20, [2], 3], [29, -21], [1, 45, 645, 930, -22]], [0, "c5sFU2N+xD84AmhVzE4uRp", 1, 0], [5, 645, 930]], [4, "title_zhua<PERSON><PERSON>", 2, [-24, -25], [[26, false, 1, 0, -23, [8], 9]], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 645, 930]], [6, "btn_coutinue", 2, [-28], [[34, 0.9, 3, -26, [[3, "88486GjA5dIGbBp/5ecunKP", "onGetAll", 1]], [4, 4293322470], [4, 3363338360], 12, 13], [8, 1, 0, -27, [14], 15]], [0, "baZwZnr4BFeahDVKhdqBXc", 1, 0], [5, 260, 108], [0, -378.953, 0, 0, 0, 0, 1, 1, 1, 0]], [22, "New ScrollView", 2, [-31], [[[38, false, 0.75, 0.23, null, null, -29, [[3, "88486GjA5dIGbBp/5ecunKP", "onScroll", 1]], 4], -30], 4, 1], [0, "80Rw9Ag6xP+pHQk4tJjt/Y", 1, 0], [5, 605, 650], [0, 31.087, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "view", 8, [4], [[37, 0, -32, [23]], [1, 45, 240, 250, -33]], [0, "0cceNQOh1M+4fVi8na27Y1", 1, 0], [5, 605, 650], [0, 0.5, 1], [0, 325, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "maskbg", 70, 1, [[23, 45, -34], [7, 0, -35, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [4, "bg", 1, [2], [[1, 45, 750, 1334, -36]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [5, "Label_title", 6, [[30, "章节奖励", 48, 48, false, 1, 1, 1, -37, [6], 7], [2, 3, -38, [4, 4279374353]]], [0, "b9rqLMaz5Cn6ICQOUKICkQ", 1, 0], [5, 198, 66.47999999999999], [0, 420.516, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Label", 7, [[31, "全部领取", 36, false, false, 1, 1, 1, -39, [10], 11], [2, 2, -40, [4, 4278190080]]], [0, "99+SJAXOBM15DIFNxsP80M", 1, 0], [5, 148, 54.4], [0, 3.08, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnclose", 2, [3], [[35, 3, -41, [[3, "88486GjA5dIGbBp/5ecunKP", "close", 1]], [4, 4293322470], [4, 3363338360], 3, 20]], [0, "84QCINS25D8q6Qi3hyRYHE", 1, 0], [5, 80, 80], [249.321, 414.898, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "lbType", false, 2, [[-42, [2, 3, -43, [4, 4279374353]]], 1, 4], [0, "b6IwZ/8h9ANpA/MJC9WGvt", 1, 0], [5, 181.11, 56.4], [-205.224, 420.516, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "DiffSelect", 2, [-44], [0, "47cQecJfhK/Zurz1m8BN0A", 1, 0], [-128.472, 432.348, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "img_fgx", false, 62, 6, [[7, 0, -45, [4], 5]], [0, "08ZpR4lZFKY7+GPeCJk8EK", 1, 0], [4, 4285252343], [5, 600, 655], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "Label", false, 3, [[32, "返回", false, false, 1, 1, 1, 1, -46, [16], 17]], [0, "73tt8URgtBIp2EHmqVFI9W", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "【难度:困难】", 28, false, 1, 1, 1, 15, [21]], [18, "New RichText", 16, [[36, false, "<outline color=black width=3>难度:<color=#00ff00>普通</c>", 32, 50, -47, 22]], [0, "8bgHj+zRBCyoag1O5f5to3", 1, 0], [5, 148.03, 63], [0, 1, 0.5], [-17.153, -10.807, 0, 0, 0, 0, 1, 1, 1, 1]], [39, 8, [0, 0, 20], 4]], 0, [0, 8, 1, 0, 9, 16, 0, 10, 21, 0, 11, 4, 0, -1, 19, 0, 0, 1, 0, -1, 10, 0, -2, 11, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 14, 0, -5, 15, 0, -6, 16, 0, -7, 8, 0, 0, 3, 0, 0, 3, 0, -1, 18, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, -1, 17, 0, -2, 12, 0, 0, 7, 0, 0, 7, 0, -1, 13, 0, 0, 8, 0, -2, 21, 0, -1, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, -1, 19, 0, 0, 15, 0, -1, 20, 0, 0, 17, 0, 0, 18, 0, 0, 20, 0, 12, 1, 2, 3, 11, 3, 3, 14, 4, 3, 9, 47], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 21], [-1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 2, 5, 4, -1, 1, -1, 2, -1, 1, 4, -1, 6, -1, 2, 7], [0, 5, 0, 1, 0, 6, 0, 2, 0, 1, 0, 2, 7, 3, 0, 8, 0, 4, 0, 9, 3, 0, 10, 0, 4, 11]]