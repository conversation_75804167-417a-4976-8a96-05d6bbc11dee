[1, ["7aA+1UZYVMoI6XM01IDPEe"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "shizi", "\nshizi.png\nsize: 550,1066\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\ns0\n  rotate: true\n  xy: 340, 487\n  size: 172, 208\n  orig: 172, 208\n  offset: 0, 0\n  index: -1\ns1\n  rotate: false\n  xy: 2, 513\n  size: 336, 254\n  orig: 336, 254\n  offset: 0, 0\n  index: -1\ns2\n  rotate: true\n  xy: 184, 342\n  size: 169, 151\n  orig: 169, 151\n  offset: 0, 0\n  index: -1\ns3\n  rotate: false\n  xy: 2, 769\n  size: 350, 295\n  orig: 350, 295\n  offset: 0, 0\n  index: -1\ntw_00\n  rotate: false\n  xy: 354, 772\n  size: 181, 89\n  orig: 183, 109\n  offset: 1, 9\n  index: -1\ntw_01\n  rotate: false\n  xy: 2, 2\n  size: 172, 104\n  orig: 183, 109\n  offset: 10, 0\n  index: -1\ntw_02\n  rotate: true\n  xy: 294, 120\n  size: 175, 99\n  orig: 183, 109\n  offset: 2, 0\n  index: -1\ntw_03\n  rotate: false\n  xy: 345, 11\n  size: 172, 107\n  orig: 183, 109\n  offset: 1, 0\n  index: -1\ntw_04\n  rotate: true\n  xy: 183, 164\n  size: 176, 109\n  orig: 183, 109\n  offset: 0, 0\n  index: -1\ntw_05\n  rotate: false\n  xy: 354, 661\n  size: 180, 109\n  orig: 183, 109\n  offset: 0, 0\n  index: -1\ntw_06\n  rotate: false\n  xy: 354, 955\n  size: 181, 109\n  orig: 183, 109\n  offset: 0, 0\n  index: -1\ntw_07\n  rotate: false\n  xy: 337, 392\n  size: 179, 93\n  orig: 183, 109\n  offset: 4, 16\n  index: -1\ntw_08\n  rotate: false\n  xy: 337, 297\n  size: 178, 93\n  orig: 183, 109\n  offset: 5, 16\n  index: -1\ntw_09\n  rotate: false\n  xy: 2, 420\n  size: 180, 91\n  orig: 183, 109\n  offset: 0, 18\n  index: -1\ntw_10\n  rotate: true\n  xy: 395, 125\n  size: 170, 90\n  orig: 183, 109\n  offset: 4, 19\n  index: -1\ntw_11\n  rotate: false\n  xy: 2, 214\n  size: 176, 98\n  orig: 183, 109\n  offset: 0, 11\n  index: -1\ntw_12\n  rotate: false\n  xy: 2, 314\n  size: 179, 104\n  orig: 183, 109\n  offset: 4, 5\n  index: -1\ntw_13\n  rotate: false\n  xy: 176, 2\n  size: 167, 104\n  orig: 183, 109\n  offset: 16, 5\n  index: -1\ntw_14\n  rotate: false\n  xy: 2, 108\n  size: 175, 104\n  orig: 183, 109\n  offset: 8, 4\n  index: -1\ntw_15\n  rotate: false\n  xy: 354, 863\n  size: 181, 90\n  orig: 183, 109\n  offset: 2, 7\n  index: -1\n", ["shizi.png"], {"skeleton": {"hash": "4yegRV/ANlLZSYxDFjgjA+SqSPE", "spine": "3.8.99", "x": -174.56, "y": -215.19, "width": 350, "height": 398, "images": "./images/", "audio": "J:/工作/我要上巅峰/青龙/正/../../狮子"}, "bones": [{"name": "root"}, {"name": "all", "parent": "root", "color": "000000ff"}, {"name": "zz", "parent": "all", "color": "000000ff"}, {"name": "s1", "parent": "zz", "length": 68.63, "rotation": 90, "x": 2.32, "y": -93.68, "color": "32ff00ff"}, {"name": "s2", "parent": "zz", "length": 55.21, "rotation": -89.46, "x": 1.29, "y": -80.26, "color": "ffeb00ff"}, {"name": "s3", "parent": "s2", "length": 15.82, "rotation": -27.98, "x": 54.47, "y": -43.17, "color": "ffeb00ff"}, {"name": "s4", "parent": "s3", "length": 15.13, "rotation": 62.26, "x": 15.82, "color": "ffeb00ff"}, {"name": "s5", "parent": "s4", "length": 14.35, "rotation": -18.44, "x": 15.13, "color": "ffeb00ff"}, {"name": "s6", "parent": "s5", "length": 15.47, "rotation": -60.68, "x": 14.35, "color": "ffeb00ff"}, {"name": "s7", "parent": "s2", "length": 16.37, "rotation": 32, "x": 56.16, "y": 43.1, "color": "ffeb00ff"}, {"name": "s8", "parent": "s7", "length": 17.85, "rotation": -33.3, "x": 16.37, "color": "ffeb00ff"}, {"name": "s9", "parent": "s8", "length": 20.41, "rotation": -57.58, "x": 17.85, "color": "ffeb00ff"}, {"name": "s10", "parent": "s1", "length": 27.02, "rotation": 79.38, "x": 54.17, "y": 80.32, "color": "32ff00ff"}, {"name": "s11", "parent": "s10", "length": 27.92, "rotation": 15.74, "x": 27.02, "color": "32ff00ff"}, {"name": "s12", "parent": "s11", "length": 34.39, "rotation": 12.44, "x": 27.5, "y": 0.04, "color": "32ff00ff"}, {"name": "s13", "parent": "s1", "length": 30.9, "rotation": -80.17, "x": 54.53, "y": -72.82, "color": "32ff00ff"}, {"name": "s14", "parent": "s13", "length": 30.11, "rotation": -13.7, "x": 30.9, "color": "32ff00ff"}, {"name": "s15", "parent": "s14", "length": 34.03, "rotation": -13.49, "x": 30.11, "color": "32ff00ff"}, {"name": "s0", "parent": "s1", "length": 62.68, "rotation": 2.66, "x": 75.46, "y": 0.24, "color": "ff0000ff"}, {"name": "s16", "parent": "s0", "length": 25.93, "rotation": -15.5, "x": 66.11, "y": -3.32, "color": "ff0000ff"}, {"name": "s17", "parent": "s16", "length": 36.73, "rotation": -5.44, "x": 25.93, "color": "ff0000ff"}, {"name": "s18", "parent": "s17", "length": 25.07, "rotation": -1.71, "x": 42.2, "y": -0.53, "color": "ff0000ff"}, {"name": "s19", "parent": "s18", "length": 32.8, "rotation": 11.4, "x": 25.07, "color": "ff0000ff"}, {"name": "s20", "parent": "s19", "length": 27.16, "rotation": -58.9, "x": 32.8, "color": "ff0000ff"}, {"name": "s21", "parent": "s16", "length": 24.09, "rotation": 25.28, "x": 17.33, "y": 67.73, "color": "ff0000ff"}, {"name": "s22", "parent": "s21", "length": 21.18, "rotation": 39.19, "x": 24.09, "color": "ff0000ff"}, {"name": "s23", "parent": "s22", "length": 23.83, "rotation": -38.2, "x": 21.18, "color": "ff0000ff"}, {"name": "s24", "parent": "s17", "length": 19.53, "rotation": 25.4, "x": 36.8, "y": 76.42, "color": "ff0000ff"}, {"name": "s25", "parent": "s24", "length": 20.12, "rotation": 33.69, "x": 19.53, "color": "ff0000ff"}, {"name": "s26", "parent": "zz", "length": 196.84, "rotation": -89.58, "x": -5.99, "y": 60.21, "color": "0062ffff"}, {"name": "s27", "parent": "s26", "length": 20.01, "rotation": -1.86, "x": 201.19, "y": 1.84, "color": "0062ffff"}, {"name": "s28", "parent": "s27", "length": 23.01, "rotation": -0.44, "x": 20.01, "color": "0062ffff"}, {"name": "s29", "parent": "s28", "length": 21.76, "rotation": 3.84, "x": 23.01, "color": "0062ffff"}, {"name": "s30", "parent": "s26", "length": 21.51, "rotation": -0.42, "x": 199.22, "y": -65.63, "color": "0062ffff"}, {"name": "s31", "parent": "s30", "length": 27.89, "rotation": -73.37, "x": 21.51, "color": "0062ffff"}, {"name": "s32", "parent": "s26", "length": 26.64, "rotation": -56.01, "x": 168.06, "y": -94.49, "color": "0062ffff"}, {"name": "s33", "parent": "s32", "length": 28.68, "rotation": -40.94, "x": 26.64, "color": "0062ffff"}, {"name": "s34", "parent": "s26", "length": 26.03, "rotation": -100.65, "x": 129.05, "y": -132.13, "color": "0062ffff"}, {"name": "s35", "parent": "s26", "length": 24.41, "rotation": -107.78, "x": 56.82, "y": -117.55, "color": "0062ffff"}, {"name": "s36", "parent": "s35", "length": 27.7, "rotation": 20.37, "x": 24.41, "color": "0062ffff"}, {"name": "s37", "parent": "s26", "length": 19.1, "rotation": 0.58, "x": 217.09, "y": 42.57, "color": "0062ffff"}, {"name": "s38", "parent": "s37", "length": 20.69, "rotation": 59.94, "x": 19.1, "color": "0062ffff"}, {"name": "s39", "parent": "s26", "length": 18.47, "rotation": 3.74, "x": 201.62, "y": 79.87, "color": "0062ffff"}, {"name": "s40", "parent": "s39", "length": 20.42, "rotation": 44.83, "x": 18.47, "color": "0062ffff"}, {"name": "s41", "parent": "s26", "length": 23.51, "rotation": 46.28, "x": 174.53, "y": 121.17, "color": "0062ffff"}, {"name": "s42", "parent": "s41", "length": 24.07, "rotation": 53.53, "x": 23.51, "color": "0062ffff"}, {"name": "s43", "parent": "s26", "length": 22.42, "rotation": 87.12, "x": 132.39, "y": 149.03, "color": "0062ffff"}, {"name": "s44", "parent": "s26", "length": 15.51, "rotation": 110.43, "x": 60.46, "y": 144.7, "color": "0062ffff"}, {"name": "s45", "parent": "s44", "length": 16.94, "rotation": -17.35, "x": 15.51, "color": "0062ffff"}, {"name": "s46", "parent": "s26", "length": 28.34, "rotation": -55.78, "x": 223.55, "y": -19.47, "color": "0062ffff"}, {"name": "s47", "parent": "s46", "length": 27.14, "rotation": 56.25, "x": 28.34, "color": "0062ffff"}, {"name": "zz2", "parent": "zz", "x": 3.29, "y": -61.37, "color": "ca00ffff"}, {"name": "h1", "parent": "s1", "rotation": -144.14, "x": 96.73, "y": 77.96, "scaleX": 0.9424, "scaleY": 0.7679, "color": "32ff00ff"}, {"name": "h2", "parent": "s1", "rotation": -144.14, "x": 96.73, "y": 77.96, "scaleX": 0.9424, "scaleY": 0.7679, "color": "32ff00ff"}], "slots": [{"name": "s3", "bone": "s26", "attachment": "s3"}, {"name": "s2", "bone": "s2", "attachment": "s2"}, {"name": "s1", "bone": "s1", "attachment": "s1"}, {"name": "s0", "bone": "s0", "attachment": "s0"}, {"name": "h1", "bone": "h1", "blend": "additive"}, {"name": "h2", "bone": "h2", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"h1": {"tw_00": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_01": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_02": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_03": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_04": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_05": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_06": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_07": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_08": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_09": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_10": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_11": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_12": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_13": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_14": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_15": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}}, "h2": {"tw_00": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_01": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_02": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_03": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_04": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_05": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_06": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_07": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_08": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_09": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_10": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_11": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_12": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_13": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_14": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}, "tw_15": {"x": 0.5, "y": 0.5, "width": 183, "height": 109}}, "s0": {"s0": {"type": "mesh", "hull": 46, "width": 172, "height": 208, "uvs": [0.50859, 1, 0.49417, 0.89709, 0.33701, 0.74567, 0.34566, 0.67413, 0.22808, 0.58894, 0.15017, 0.51058, 0.08207, 0.48055, 0.07339, 0.42561, 0.11032, 0.39019, 0.00539, 0.33225, 1e-05, 0.18614, 0.02984, 0.18501, 0.06561, 0.30147, 0.18506, 0.34192, 0.24012, 0.42608, 0.2348, 0.56188, 0.24579, 0.41589, 0.20031, 0.28074, 0.12803, 0.20908, 0.16008, 0.1528, 0.08013, 0.08587, 0.12446, 0.0646, 0.19374, 0.09987, 0.25752, 0.15447, 0.28544, 0.20956, 0.27145, 0.26602, 0.20821, 0.27912, 0.2484, 0.416, 0.23728, 0.56046, 0.27516, 0.43203, 0.37528, 0.38226, 0.33583, 0.32139, 0.4452, 0.15585, 0.57799, 0.09496, 0.70679, 0, 0.88456, 3e-05, 0.95685, 0.16735, 0.99999, 0.37646, 0.91733, 0.41169, 0.82525, 0.41889, 0.93607, 0.51892, 0.86129, 0.6201, 0.7063, 0.65621, 0.71766, 0.75236, 0.57491, 0.8859, 0.55761, 1], "triangles": [12, 10, 11, 12, 9, 10, 9, 12, 8, 8, 12, 13, 14, 15, 5, 14, 5, 8, 5, 6, 8, 6, 7, 8, 14, 8, 13, 26, 27, 16, 16, 27, 28, 19, 22, 23, 20, 21, 19, 19, 21, 22, 17, 26, 16, 17, 18, 26, 26, 18, 24, 25, 26, 24, 19, 24, 18, 23, 24, 19, 35, 36, 34, 39, 32, 33, 39, 33, 36, 34, 36, 33, 38, 39, 36, 38, 36, 37, 30, 28, 29, 42, 30, 39, 39, 30, 32, 41, 39, 40, 30, 31, 32, 42, 39, 41, 4, 5, 15, 44, 45, 1, 45, 0, 1, 1, 2, 44, 44, 2, 43, 43, 3, 42, 3, 43, 2, 3, 4, 28, 3, 28, 30, 4, 15, 28, 15, 16, 28, 3, 30, 42], "vertices": [1, 18, -6.77, 4.49, 1, 1, 18, 14.73, 5.97, 1, 4, 18, 47.44, 31.51, 0.99732, 19, -27.3, 28.58, 0.00081, 20, -55.69, 23.4, 0.00177, 27, -106.29, -8.22, 0.0001, 4, 18, 62.24, 29.34, 0.86998, 19, -12.46, 30.43, 0.08498, 20, -41.1, 26.66, 0.0444, 27, -91.71, -11.54, 0.00063, 3, 18, 80.88, 48.71, 0.51416, 19, 0.33, 54.09, 0.22454, 20, -30.62, 51.42, 0.2613, 6, 18, 97.79, 61.34, 0.00158, 19, 13.24, 70.78, 0.00066, 20, -19.34, 69.25, 0.00079, 27, -53.78, 17.61, 3e-05, 24, -2.39, 4.5, 0.99694, 26, -43.05, -8.14, 0, 2, 24, 6.23, 14.59, 0.99999, 26, -34.26, 1.8, 1e-05, 3, 24, 17.71, 13.59, 0.92402, 25, 3.64, 14.57, 0.07597, 26, -22.8, 0.6, 1e-05, 3, 24, 23.53, 5.8, 0.43504, 25, 3.23, 4.85, 0.56495, 26, -17.1, -7.29, 1e-05, 2, 25, 24.86, 6.6, 0.40367, 26, -1.19, 7.46, 0.59633, 1, 26, 28.58, 1.3, 1, 1, 26, 27.62, -3.74, 1, 2, 25, 20.72, -4.85, 0.38962, 26, 2.63, -4.1, 0.61038, 2, 24, 30.57, -8.92, 0.29959, 25, -0.62, -11, 0.70041, 2, 24, 11.44, -14.39, 1, 26, -29.55, -27.27, 0, 3, 18, 86.45, 47.3, 0.48451, 19, 6.07, 54.21, 0.22804, 20, -24.91, 52.09, 0.28744, 4, 18, 116.69, 44, 0.25344, 19, 36.1, 59.11, 0.08708, 20, 4.52, 59.81, 0.10066, 24, 13.29, -15.8, 0.55882, 5, 18, 145.14, 50.51, 0.00146, 19, 61.77, 72.99, 0.00039, 20, 28.76, 76.06, 0.0004, 27, -7.42, 3.12, 0.99407, 24, 42.43, -14.22, 0.00369, 3, 27, 8.91, 13.61, 0.99881, 28, -1.28, 17.21, 0.00119, 26, 18.83, -19, 0, 2, 27, 19.85, 6.68, 0.33506, 28, 3.97, 5.38, 0.66494, 1, 28, 23.5, 6.69, 1, 2, 28, 21.86, -1.97, 1, 26, 48.2, -25.39, 0, 1, 28, 8.52, -6.19, 1, 3, 27, 17.42, -9.9, 0.94163, 28, -7.24, -7.07, 0.05837, 26, 24.7, -43.31, 0, 2, 27, 5.46, -13.25, 1, 26, 12.44, -45.32, 0, 6, 18, 147.63, 38.14, 2e-05, 19, 67.47, 61.74, 0, 20, 35.5, 65.4, 0, 27, -5.9, -9.4, 0.9999, 24, 42.79, -26.83, 6e-05, 26, 1.58, -40.25, 0, 5, 18, 145.41, 49.13, 0.00137, 19, 62.4, 71.74, 0.00037, 20, 29.51, 74.87, 0.00037, 27, -7.25, 1.73, 0.99443, 24, 42.47, -15.62, 0.00346, 4, 18, 116.65, 43.55, 0.25346, 19, 36.18, 58.67, 0.08714, 20, 4.64, 59.38, 0.10074, 24, 13.18, -16.24, 0.55866, 3, 18, 86.73, 46.86, 0.48019, 19, 6.45, 53.86, 0.22858, 20, -24.49, 51.77, 0.29123, 5, 18, 113.11, 39.11, 0.20259, 19, 33.95, 53.44, 0.19041, 20, 2.92, 53.96, 0.59605, 21, -40.89, 53.3, 0.00838, 22, -54.12, 65.28, 0.00257, 5, 18, 122.65, 21.42, 0.0589, 19, 47.87, 38.95, 0.06926, 20, 18.15, 40.86, 0.7368, 21, -25.28, 40.65, 0.09614, 22, -41.32, 49.8, 0.0389, 5, 18, 135.61, 27.62, 0.01524, 19, 58.7, 48.38, 0.01347, 20, 28.04, 51.27, 0.66922, 21, -15.7, 51.36, 0.20586, 22, -29.81, 58.4, 0.09622, 4, 20, 66.63, 44.21, 0.22107, 21, 23.09, 45.44, 0.34925, 22, 7.04, 44.94, 0.42846, 24, 58.72, -60.95, 0.00121, 4, 20, 85.83, 26.49, 0.03325, 21, 42.8, 28.31, 0.12048, 22, 22.98, 24.24, 0.84548, 23, -25.83, 4.11, 0.0008, 2, 22, 45.81, 5.29, 0.19562, 23, 2.19, 13.87, 0.80438, 1, 23, 30.44, 2.15, 1, 4, 21, 50.92, -38.08, 0.22923, 22, 17.81, -42.44, 0.38848, 23, 28.59, -34.76, 0.38228, 26, -5.84, -159.68, 0, 6, 19, 72.91, -65.54, 0.00554, 20, 52.99, -60.79, 0.07317, 21, 12.58, -59.92, 0.66124, 22, -24.09, -56.27, 0.1915, 23, 18.79, -77.78, 0.06855, 26, -49.87, -156.79, 0, 6, 19, 62.61, -53.31, 0.0231, 20, 41.57, -49.59, 0.13684, 21, 0.83, -49.06, 0.647, 22, -33.46, -43.3, 0.14462, 23, 2.85, -79.1, 0.04844, 26, -53.69, -141.26, 0, 6, 19, 57.63, -38.2, 0.12652, 20, 35.18, -35.02, 0.39531, 21, -5.99, -34.69, 0.42427, 22, -37.3, -27.86, 0.04064, 23, -12.36, -74.42, 0.01325, 26, -51.47, -125.51, 0, 5, 19, 41.58, -61.41, 0.37702, 20, 21.41, -59.65, 0.51137, 21, -19.02, -59.71, 0.11161, 22, -55.03, -49.82, 0, 26, -76.14, -139.22, 0, 4, 19, 18.2, -53.54, 0.486, 20, -2.61, -54.03, 0.45326, 21, -43.2, -54.82, 0.06074, 26, -93.62, -121.82, 0, 5, 18, 63.08, -32.8, 0.08863, 19, 4.96, -29.22, 0.73261, 20, -18.1, -31.08, 0.16982, 21, -59.37, -32.33, 0.00893, 26, -94.73, -94.14, 0, 4, 18, 43.01, -33.82, 0.50185, 19, -14.11, -35.57, 0.48778, 20, -36.48, -39.2, 0.01037, 26, -114.64, -91.4, 0, 3, 18, 16.41, -8.01, 0.9941, 19, -46.64, -17.8, 0.0059, 26, -135.95, -61.07, 0, 1, 18, -7.16, -3.93, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 12, 14, 18, 20, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 0, 90, 68, 70, 66, 68, 64, 66, 62, 64, 60, 62, 58, 60, 56, 58, 54, 56, 8, 10, 10, 12, 14, 16, 16, 18, 20, 22, 22, 24, 24, 26, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 26, 28, 28, 30, 30, 32, 32, 34]}}, "s1": {"s1": {"type": "mesh", "hull": 32, "width": 336, "height": 254, "uvs": [0.21908, 0.61069, 0.165, 0.60913, 0.10505, 0.67756, 0.04039, 0.72732, 0, 0.77242, 0, 0.80352, 0.05097, 0.77242, 0.1074, 0.78486, 0.17441, 0.85795, 0.23906, 0.8284, 0.2708, 0.85018, 0.3049, 0.91394, 0.3672, 0.92638, 0.41305, 1, 0.58234, 1, 0.62936, 0.92171, 0.69755, 0.90772, 0.73987, 0.8424, 0.81393, 0.85951, 0.88917, 0.79575, 1, 0.81441, 1, 0.76464, 0.9021, 0.69311, 0.82921, 0.61224, 0.77866, 0.61224, 0.98719, 0.35201, 0.91172, 0, 0.80792, 1e-05, 0.50332, 0.43505, 0.19649, 0, 0.07446, 0, 0.03073, 0.35387, 0.2859, 0.70971, 0.35442, 0.74756, 0.40788, 0.74856, 0.47113, 0.77445, 0.43951, 0.82625, 0.39056, 0.84717, 0.33936, 0.80334, 0.5261, 0.77445, 0.55471, 0.82625, 0.6014, 0.85215, 0.65185, 0.81928, 0.71736, 0.7127, 0.59311, 0.74856, 0.65938, 0.74756, 0.68648, 0.77246, 0.44533, 0.89079, 0.42488, 0.87833, 0.42934, 0.84968, 0.45864, 0.8362, 0.46883, 0.80671, 0.49812, 0.79155, 0.52997, 0.80587, 0.5357, 0.83452, 0.55736, 0.84294, 0.56882, 0.86064, 0.55354, 0.8876, 0.49685, 0.89097, 0.49685, 0.8362, 0.46182, 0.86401, 0.53952, 0.86485, 0.3832, 0.88405, 0.3435, 0.84996, 0.41523, 0.92827, 0.49602, 0.90892, 0.57402, 0.93011, 0.62486, 0.87115, 0.67291, 0.84628, 0.49992, 0.71495, 0.41483, 0.68507, 0.35234, 0.64324, 0.31168, 0.54761, 0.49013, 0.60738, 0.45324, 0.51773, 0.50971, 0.60937, 0.54736, 0.50777, 0.58425, 0.69204, 0.64675, 0.63626, 0.69193, 0.54164], "triangles": [19, 22, 21, 19, 21, 20, 43, 19, 18, 19, 23, 22, 19, 43, 23, 7, 6, 3, 4, 3, 6, 5, 4, 6, 9, 7, 2, 7, 3, 2, 2, 1, 9, 9, 1, 0, 8, 7, 9, 18, 17, 43, 16, 68, 17, 23, 43, 24, 27, 25, 79, 78, 79, 43, 45, 78, 43, 46, 43, 17, 42, 45, 46, 15, 68, 16, 67, 42, 68, 15, 67, 68, 10, 9, 32, 63, 10, 38, 32, 0, 72, 72, 0, 31, 32, 72, 71, 71, 72, 74, 28, 72, 29, 29, 31, 30, 79, 76, 28, 74, 28, 76, 74, 72, 28, 73, 74, 76, 72, 31, 29, 75, 73, 76, 75, 76, 78, 70, 71, 74, 73, 70, 74, 77, 75, 78, 69, 73, 75, 69, 75, 77, 70, 73, 69, 33, 71, 70, 34, 33, 70, 35, 34, 70, 69, 35, 70, 39, 69, 77, 39, 77, 44, 52, 35, 69, 39, 52, 69, 40, 53, 39, 52, 39, 53, 35, 36, 34, 51, 35, 52, 51, 36, 35, 44, 40, 39, 41, 40, 44, 54, 53, 40, 50, 36, 51, 59, 51, 52, 59, 52, 53, 59, 53, 54, 50, 51, 59, 41, 55, 40, 54, 40, 55, 34, 38, 33, 37, 34, 36, 37, 38, 34, 49, 37, 36, 63, 38, 37, 44, 42, 41, 56, 55, 41, 60, 50, 59, 61, 54, 55, 61, 55, 56, 67, 41, 42, 48, 37, 49, 49, 36, 50, 62, 63, 37, 62, 37, 48, 57, 61, 56, 60, 47, 49, 60, 49, 50, 48, 49, 47, 61, 58, 59, 60, 59, 58, 61, 59, 54, 58, 47, 60, 65, 47, 58, 12, 63, 62, 11, 63, 12, 64, 62, 48, 64, 48, 47, 12, 62, 64, 66, 56, 41, 66, 41, 67, 66, 67, 15, 57, 56, 66, 13, 12, 64, 14, 66, 15, 65, 58, 57, 58, 61, 57, 65, 57, 66, 14, 65, 66, 64, 47, 65, 65, 13, 64, 14, 13, 65, 32, 71, 33, 38, 32, 33, 9, 0, 32, 38, 10, 32, 11, 10, 63, 78, 76, 79, 44, 77, 45, 42, 44, 45, 77, 78, 45, 68, 42, 46, 46, 45, 43, 17, 68, 46, 27, 79, 28, 43, 79, 24, 25, 24, 79, 25, 27, 26], "vertices": [2, 3, 89.37, 93.28, 0.944, 12, 19.22, -32.22, 0.056, 3, 3, 89.77, 111.45, 0.0156, 12, 37.15, -29.26, 0.41157, 13, 1.82, -30.91, 0.57284, 3, 12, 53.75, -8.46, 0.00674, 13, 23.43, -15.39, 0.82783, 14, -7.3, -14.19, 0.16543, 1, 14, 17.23, -8.69, 1, 1, 14, 33.62, -1.87, 1, 2, 13, 61.44, 13.33, 0, 14, 36, 5.66, 1, 2, 13, 43.68, 6.99, 0.00061, 14, 17.29, 3.3, 0.99939, 3, 12, 47.95, 18.18, 0.02431, 13, 25.07, 11.82, 0.71246, 14, 0.17, 12.03, 0.26323, 2, 12, 22.4, 32.28, 0.52468, 13, 4.31, 32.32, 0.47532, 3, 3, 34.07, 86.56, 0.03102, 12, 2.43, 20.9, 0.89496, 13, -18, 26.79, 0.07402, 2, 3, 28.54, 75.9, 0.928, 12, -9.07, 24.37, 0.072, 1, 3, 12.35, 64.44, 1, 1, 3, 9.19, 43.51, 1, 1, 3, -9.51, 28.1, 1, 1, 3, -9.51, -28.78, 1, 1, 3, 10.37, -44.58, 1, 1, 3, 13.93, -67.49, 1, 2, 3, 30.52, -81.71, 0.93926, 15, 4.66, -25.18, 0.06074, 2, 15, 28.44, -33.71, 0.48962, 16, 5.59, -33.34, 0.51038, 3, 15, 56.11, -22.07, 0.02248, 16, 29.72, -15.47, 0.48059, 17, 3.23, -15.14, 0.49693, 1, 17, 40.18, -8.56, 1, 2, 16, 66.34, -5.08, 0, 17, 36.41, 3.51, 1, 2, 16, 32.3, 10.83, 0.55552, 17, -0.4, 11.04, 0.44448, 3, 3, 88.98, -111.73, 0.00782, 15, 44.22, 27.29, 0.33742, 16, 6.48, 29.67, 0.65475, 2, 3, 88.98, -94.74, 0.92, 15, 27.49, 30.2, 0.08, 1, 3, 155.08, -164.81, 1, 1, 3, 244.49, -139.45, 1, 1, 3, 244.49, -104.57, 1, 1, 3, 133.99, -2.23, 1, 1, 3, 244.49, 100.87, 1, 1, 3, 244.49, 141.87, 1, 1, 3, 154.6, 156.56, 1, 2, 3, 64.22, 70.82, 0.95, 51, -71.79, 31.92, 0.05, 2, 3, 54.61, 47.8, 0.95, 51, -48.77, 22.3, 0.05, 2, 3, 54.36, 29.84, 0.95, 51, -30.81, 22.05, 0.05, 2, 3, 47.78, 8.59, 0.95, 51, -9.55, 15.47, 0.05, 2, 3, 34.62, 19.21, 0.95, 51, -20.18, 2.31, 0.05, 2, 3, 29.31, 35.66, 0.95, 51, -36.62, -3, 0.05, 2, 3, 40.44, 52.86, 0.95, 51, -53.83, 8.13, 0.05, 2, 3, 47.78, -9.88, 0.95, 51, 8.92, 15.47, 0.05, 2, 3, 34.62, -19.5, 0.9, 51, 18.53, 2.31, 0.1, 2, 3, 28.04, -35.18, 0.95, 51, 34.22, -4.26, 0.05, 2, 3, 36.39, -52.13, 0.95, 51, 51.17, 4.09, 0.05, 2, 3, 63.46, -74.14, 0.95, 51, 73.18, 31.16, 0.05, 2, 3, 54.35, -32.4, 0.95, 51, 31.43, 22.05, 0.05, 2, 3, 54.61, -54.66, 0.95, 51, 53.7, 22.3, 0.05, 2, 3, 48.28, -63.77, 0.95, 51, 62.8, 15.98, 0.05, 2, 3, 18.23, 17.26, 0.9, 51, -18.22, -14.08, 0.1, 2, 3, 21.39, 24.13, 0.9, 51, -25.09, -10.91, 0.1, 2, 3, 28.67, 22.63, 0.9, 51, -23.6, -3.64, 0.1, 2, 3, 32.09, 12.79, 0.9, 51, -13.75, -0.21, 0.1, 2, 3, 39.58, 9.36, 0.9, 51, -10.33, 7.28, 0.1, 2, 3, 43.43, -0.48, 0.9, 51, -0.48, 11.13, 0.1, 2, 3, 39.8, -11.18, 0.9, 51, 10.22, 7.49, 0.1, 2, 3, 32.52, -13.11, 0.9, 51, 12.14, 0.21, 0.1, 2, 3, 30.38, -20.38, 0.9, 51, 19.42, -1.93, 0.1, 2, 3, 25.89, -24.24, 0.9, 51, 23.27, -6.42, 0.1, 2, 3, 19.04, -19.1, 0.9, 51, 18.13, -13.27, 0.1, 2, 3, 18.18, -0.05, 0.85, 51, -0.91, -14.12, 0.15, 2, 3, 32.09, -0.05, 0.9, 51, -0.91, -0.21, 0.1, 2, 3, 25.03, 11.72, 0.85, 51, -12.68, -7.28, 0.15, 2, 3, 24.82, -14.39, 0.85, 51, 13.43, -7.49, 0.15, 1, 3, 19.94, 38.13, 1, 1, 3, 28.6, 51.47, 1, 1, 3, 8.71, 27.37, 1, 3, 3, 13.62, 0.22, 0.9991, 12, -86.2, 25.09, 0.00072, 15, -78.95, -27.84, 0.00018, 1, 3, 8.24, -25.98, 1, 1, 3, 23.22, -43.07, 1, 1, 3, 29.53, -59.21, 1, 2, 3, 62.89, -1.09, 0.95, 51, 0.12, 30.58, 0.05, 2, 3, 70.48, 27.5, 0.95, 51, -28.47, 38.17, 0.05, 2, 3, 81.11, 48.5, 0.95, 51, -49.47, 48.8, 0.05, 2, 3, 105.39, 62.16, 0.95, 51, -63.13, 73.09, 0.05, 2, 3, 90.21, 2.2, 0.95, 51, -3.17, 57.91, 0.05, 2, 3, 112.98, 14.6, 0.95, 51, -15.57, 80.68, 0.05, 2, 3, 89.71, -4.38, 0.95, 51, 3.41, 57.4, 0.05, 2, 3, 115.51, -17.02, 0.95, 51, 16.06, 83.21, 0.05, 2, 3, 68.71, -29.42, 0.95, 51, 28.45, 36.4, 0.05, 2, 3, 82.88, -50.42, 0.95, 51, 49.45, 50.57, 0.05, 2, 3, 106.91, -65.6, 0.95, 51, 64.63, 74.61, 0.05], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 50, 52, 52, 54, 56, 58, 58, 60, 60, 62, 0, 62, 48, 50, 54, 56]}}, "s2": {"s2": {"type": "mesh", "hull": 26, "width": 169, "height": 151, "uvs": [0.84487, 0, 0.14209, 0, 0.0151, 0, 0, 0.29148, 0.03262, 0.39439, 0.14865, 0.4728, 0.08735, 0.69578, 0.13552, 0.8281, 0.18149, 0.92366, 0.1596, 1, 0.29096, 1, 0.35226, 0.88446, 0.40043, 0.78399, 0.32599, 0.73498, 0.49238, 0.67128, 0.64564, 0.72518, 0.71569, 0.80359, 0.62155, 0.86485, 0.70913, 0.96287, 0.86019, 0.91631, 0.91711, 0.75214, 0.98936, 0.60757, 0.86895, 0.60022, 0.82735, 0.54876, 1, 0.32823, 1, 0, 0.15084, 0.28657, 0.30191, 0.42134, 0.67848, 0.43359, 0.82078, 0.29148], "triangles": [18, 17, 16, 18, 16, 19, 19, 16, 20, 20, 22, 21, 22, 16, 15, 16, 22, 20, 10, 8, 11, 9, 8, 10, 11, 13, 12, 11, 8, 13, 7, 6, 13, 23, 29, 24, 22, 15, 23, 15, 28, 23, 5, 26, 27, 5, 27, 13, 6, 5, 13, 4, 3, 26, 1, 27, 26, 1, 0, 27, 29, 0, 25, 2, 26, 3, 26, 2, 1, 27, 0, 28, 29, 28, 0, 14, 27, 28, 15, 14, 28, 13, 27, 14, 5, 4, 26, 8, 7, 13, 28, 29, 23, 24, 29, 25], "vertices": [1, 4, -40.51, 60.31, 1, 1, 4, -41.62, -58.46, 1, 1, 4, -41.82, -79.92, 1, 1, 4, 2.17, -82.88, 1, 1, 4, 17.76, -77.51, 1, 2, 4, 29.78, -58.01, 0.936, 5, -14.84, -24.69, 0.064, 2, 5, 19.81, -18.37, 0.72982, 6, -14.4, -12.08, 0.27018, 3, 6, 6.65, -16.81, 0.80253, 7, -2.73, -18.63, 0.10569, 8, 7.88, -24.01, 0.09178, 3, 6, 22.94, -18.67, 0.13066, 7, 13.31, -15.25, 0.13243, 8, 12.78, -8.37, 0.73691, 1, 8, 23.62, -2.97, 1, 1, 8, 8.11, 12.92, 1, 4, 4, 92.26, -24.19, 0.0011, 5, 24.47, 34.5, 0.01132, 7, 15.77, 14.11, 0.90083, 8, -11.61, 8.15, 0.08675, 4, 4, 77.17, -15.9, 0.01809, 5, 7.25, 34.73, 0.07706, 6, 26.75, 23.75, 0.10866, 7, 3.52, 26.2, 0.7962, 2, 4, 69.65, -28.41, 0.944, 5, 6.48, 20.15, 0.056, 1, 4, 60.29, -0.21, 1, 2, 4, 68.67, 25.62, 0.952, 9, 1.34, -21.46, 0.048, 4, 4, 80.62, 37.35, 0.01224, 9, 17.69, -17.84, 0.18531, 10, 10.9, -14.19, 0.28426, 11, 8.25, -13.48, 0.51819, 1, 11, 26.65, -13.95, 1, 1, 11, 21.82, 6.41, 1, 2, 10, 27.59, 10.45, 0.35115, 11, -3.6, 13.83, 0.64885, 2, 9, 29.45, 15.03, 0.34964, 10, 2.67, 19.74, 0.65036, 2, 9, 17.61, 37.07, 0.78205, 10, -19.32, 31.66, 0.21795, 3, 4, 50.16, 63.53, 0.00726, 9, 5.73, 20.51, 0.91679, 10, -20.16, 11.3, 0.07595, 2, 4, 42.32, 56.57, 0.936, 9, -4.6, 18.76, 0.064, 1, 4, 9.3, 86.06, 1, 1, 4, -40.27, 86.53, 1, 1, 4, 1.66, -57.38, 1, 1, 4, 22.25, -32.04, 1, 1, 4, 24.7, 31.58, 1, 1, 4, 3.46, 55.83, 1], "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 4, 2, 2, 0, 0, 50]}}, "s3": {"s3": {"type": "mesh", "hull": 96, "width": 350, "height": 295, "uvs": [0.14538, 0.0769, 0.11409, 0.06622, 0.02366, 0.10639, 0.09995, 0.113, 0.13638, 0.15368, 0.09781, 0.21215, 0.05709, 0.21215, 0, 0.24063, 0, 0.26808, 0.05238, 0.25232, 0.11323, 0.3113, 0.07766, 0.35961, 0.05881, 0.42164, 0.09909, 0.45622, 0.07852, 0.48978, 0.02281, 0.4669, 0.04681, 0.53198, 0.09952, 0.53961, 0.11538, 0.59351, 0.15266, 0.62808, 0.12781, 0.66419, 0.05109, 0.65452, 0.08752, 0.71452, 0.14752, 0.71757, 0.22423, 0.70283, 0.25252, 0.75144, 0.27009, 0.79568, 0.21181, 0.83839, 0.25252, 0.86229, 0.32581, 0.83127, 0.34252, 0.77839, 0.37766, 0.8211, 0.33695, 0.87398, 0.33952, 0.93449, 0.39266, 0.99957, 0.37252, 0.91263, 0.43166, 0.87856, 0.44152, 0.91415, 0.47795, 0.95025, 0.45695, 1, 0.49509, 1, 0.51352, 0.94822, 0.55038, 0.97161, 0.5868, 0.95229, 0.59023, 0.90398, 0.55338, 0.91924, 0.54438, 0.875, 0.57523, 0.82974, 0.57909, 0.87907, 0.62452, 0.90703, 0.66995, 0.90347, 0.69009, 0.84195, 0.65752, 0.86941, 0.6378, 0.84957, 0.66138, 0.78957, 0.69309, 0.8272, 0.74709, 0.8628, 0.81395, 0.84957, 0.80195, 0.82568, 0.76466, 0.83534, 0.74709, 0.79466, 0.79295, 0.73212, 0.83409, 0.70161, 0.8808, 0.72856, 0.92623, 0.72195, 0.98495, 0.65636, 0.92152, 0.68229, 0.87952, 0.66958, 0.85938, 0.63449, 0.90309, 0.60294, 0.91423, 0.54853, 0.95195, 0.54599, 0.98409, 0.51345, 0.99266, 0.44175, 0.9648, 0.49006, 0.93695, 0.49972, 0.9048, 0.47023, 0.96095, 0.41786, 0.9438, 0.36752, 0.91252, 0.32684, 0.94295, 0.29582, 0.96009, 0.26735, 1, 0.2643, 1, 0.23989, 0.9558, 0.21803, 0.93052, 0.22921, 0.91466, 0.17735, 0.87952, 0.15853, 0.90695, 0.12192, 0.95623, 0.11481, 0.93352, 0.08082, 0.8868, 0.0798, 0.86752, 0.0315, 0.81095, 0.00353, 0.51037, 0, 0.15851, 0], "triangles": [80, 85, 81, 85, 84, 81, 82, 81, 83, 81, 84, 83, 79, 85, 80, 85, 87, 86, 70, 75, 71, 71, 74, 72, 71, 75, 74, 72, 74, 73, 63, 66, 64, 63, 67, 66, 64, 66, 65, 62, 67, 63, 57, 59, 58, 57, 56, 59, 55, 60, 56, 56, 60, 59, 55, 54, 60, 49, 52, 50, 49, 53, 52, 53, 49, 48, 50, 52, 51, 48, 47, 53, 39, 38, 40, 40, 38, 41, 42, 45, 43, 42, 41, 45, 43, 45, 44, 41, 38, 46, 45, 41, 46, 46, 38, 37, 37, 36, 46, 33, 35, 34, 33, 32, 35, 35, 32, 31, 28, 26, 29, 28, 27, 26, 29, 26, 30, 22, 20, 23, 22, 21, 20, 23, 20, 19, 16, 14, 17, 16, 15, 14, 9, 6, 5, 8, 7, 9, 9, 7, 6, 0, 3, 1, 91, 93, 92, 88, 91, 90, 88, 90, 89, 91, 87, 93, 79, 94, 87, 76, 78, 77, 94, 79, 76, 70, 76, 75, 62, 61, 68, 68, 61, 54, 60, 54, 61, 53, 47, 54, 54, 47, 30, 46, 36, 47, 35, 31, 36, 47, 36, 31, 47, 31, 30, 26, 25, 30, 13, 11, 10, 30, 94, 54, 30, 24, 94, 0, 95, 94, 76, 54, 94, 94, 19, 10, 0, 94, 4, 4, 94, 10, 4, 3, 0, 10, 9, 5, 10, 5, 4, 10, 19, 13, 12, 11, 13, 13, 19, 17, 14, 13, 17, 19, 18, 17, 24, 23, 19, 24, 19, 94, 30, 25, 24, 54, 76, 68, 62, 68, 67, 70, 69, 68, 70, 68, 76, 76, 79, 78, 93, 87, 94, 85, 79, 87, 87, 91, 88, 3, 2, 1], "vertices": [1, 29, 2.21, -117.72, 1, 1, 29, -1.02, -128.64, 1, 1, 29, 10.6, -160.38, 1, 1, 29, 12.75, -133.69, 1, 2, 29, 24.84, -121.03, 0.872, 38, 13.09, -29.39, 0.128, 2, 29, 41.99, -134.66, 0.92, 38, 20.83, -8.9, 0.08, 2, 38, 34.43, -4.65, 0.03894, 39, 7.78, -7.85, 0.96106, 1, 39, 28.17, -0.51, 1, 2, 38, 48.58, 17.06, 1e-05, 39, 28.6, 7.58, 0.99999, 2, 38, 32.47, 7.15, 0.00732, 39, 10.05, 3.9, 0.99268, 2, 29, 71.28, -129.48, 0.84, 38, 6.95, 17.41, 0.16, 2, 29, 85.44, -142.03, 0.96, 38, 14.58, 34.72, 0.04, 2, 29, 103.69, -148.77, 0.92, 37, 21.04, -21.85, 0.08, 2, 29, 113.99, -134.74, 0.84, 37, 5.35, -14.32, 0.16, 3, 29, 123.84, -142.02, 0.00013, 38, 2.84, 71.28, 0.00841, 37, 10.68, -3.3, 0.99146, 3, 29, 116.94, -161.47, 0, 38, 23.47, 70.66, 0, 37, 31.07, -6.48, 1, 1, 37, 19.39, 10.93, 1, 2, 29, 138.59, -134.78, 0.904, 37, 0.84, 9.87, 0.096, 3, 29, 154.53, -129.34, 0.89107, 37, -7.45, 24.53, 0.06093, 35, 21.33, -30.7, 0.048, 2, 29, 164.83, -116.37, 0.912, 35, 16.33, -14.91, 0.088, 3, 37, -15.43, 44.28, 0.00772, 35, 29.53, -11.04, 0.14452, 36, 9.41, -6.45, 0.84776, 2, 36, 36.41, -6.23, 1, 33, -26.21, -86.55, 0, 3, 35, 49.55, -6.76, 0.00018, 36, 21.74, 9.9, 0.99981, 33, -8.51, -73.8, 1e-05, 3, 35, 32.74, 5.85, 0.26114, 36, 0.77, 8.41, 0.73868, 33, -7.61, -52.8, 0.00018, 2, 29, 187.06, -91.48, 0.888, 35, 8.13, 17.44, 0.112, 3, 29, 201.48, -81.69, 0.84538, 35, 8.07, 34.86, 0.04262, 33, 2.38, -16.05, 0.112, 3, 35, 10.37, 49.11, 0.00675, 33, 15.43, -9.9, 0.43662, 34, 7.74, -8.66, 0.55663, 1, 34, 30.89, -2.43, 1, 3, 33, 35.08, -16.05, 0.00031, 34, 19.26, 8.41, 0.99969, 49, 57.63, -26.34, 0, 4, 29, 225.21, -56.22, 0.01223, 33, 25.93, 9.6, 0.59915, 34, -7.94, 6.98, 0.34888, 49, 31.32, -19.29, 0.03974, 2, 29, 209.66, -50.25, 0.936, 33, 10.33, 15.45, 0.064, 2, 29, 222.35, -38.04, 0.92, 49, 14.69, -11.44, 0.08, 4, 29, 237.84, -52.41, 2e-05, 33, 38.53, 13.5, 0.00035, 49, 35.28, -6.71, 0.321, 50, -1.72, -9.49, 0.67864, 1, 50, 16.14, -8.87, 1, 4, 49, 40.29, 34.86, 0.00109, 50, 35.63, 9.43, 0.99879, 31, 55.18, -32.19, 9e-05, 32, 29.94, -34.28, 3e-05, 4, 49, 31.51, 9.75, 0.02478, 50, 9.87, 2.78, 0.97244, 31, 29.78, -40.07, 0.00201, 32, 4.06, -40.44, 0.00077, 3, 29, 239.44, -19.27, 0.85338, 49, 8.77, 13.25, 0.09062, 31, 19.06, -19.71, 0.056, 3, 49, 11.9, 23.85, 0.22357, 31, 29.44, -15.92, 0.25352, 32, 5.34, -16.32, 0.52291, 2, 49, 7.47, 39.86, 0.00809, 32, 16.43, -3.94, 0.99191, 1, 32, 30.84, -11.79, 1, 3, 31, 54.14, 3.64, 0.00019, 32, 31.3, 1.55, 0.99978, 40, 57.93, -40.5, 3e-05, 3, 31, 38.66, 9.59, 0.06689, 32, 16.26, 8.52, 0.92402, 40, 42.77, -33.78, 0.00909, 3, 31, 45.14, 22.71, 0.08054, 32, 23.6, 21.18, 0.90889, 40, 49.89, -21, 0.01057, 3, 31, 39.03, 35.27, 0.08673, 32, 18.34, 34.12, 0.90267, 40, 44.42, -8.15, 0.0106, 3, 31, 24.74, 36, 0.09398, 32, 4.14, 35.81, 0.89426, 40, 30.19, -6.7, 0.01176, 3, 31, 29.66, 23.26, 0.18483, 32, 8.19, 22.76, 0.78132, 40, 34.47, -19.68, 0.03385, 4, 30, 36.88, 19.55, 0.0269, 31, 16.72, 19.68, 0.5574, 32, -4.96, 20.06, 0.23705, 40, 21.36, -22.6, 0.17866, 3, 29, 225.41, 31.08, 0.83904, 31, 3.03, 30.04, 0.07296, 40, 8.2, -11.57, 0.088, 4, 30, 37.77, 31.73, 0.0089, 31, 17.52, 31.87, 0.01652, 40, 22.78, -10.47, 0.62146, 41, -7.22, -8.43, 0.35311, 1, 41, 10.68, -7.92, 1, 1, 41, 24.07, 0.72, 1, 4, 40, 12.51, 28.56, 0.00083, 41, 21.42, 20.01, 0.99912, 42, 27.07, -10.4, 4e-05, 43, -1.24, -13.43, 1e-05, 4, 40, 20.41, 17.02, 0.00639, 41, 15.39, 7.39, 0.99325, 42, 34.32, -22.36, 0.00035, 43, -4.52, -27.03, 0, 4, 40, 14.44, 10.22, 0.49677, 41, 6.51, 9.16, 0.48244, 42, 27.98, -28.81, 0.02077, 43, -13.57, -27.14, 2e-05, 3, 29, 213.79, 61.32, 0.86861, 40, -3.12, 18.78, 0.06739, 42, 10.93, -19.3, 0.064, 3, 40, 8.18, 29.69, 0.07918, 42, 22.81, -9.04, 0.47217, 43, -3.3, -9.46, 0.44865, 1, 43, 17.85, -4.98, 1, 5, 40, 15.52, 71.86, 0.00012, 42, 32.46, 32.67, 0.00011, 43, 32.95, 13.31, 0.99977, 44, 34.88, -45.99, 1e-05, 45, -30.23, -36.48, 0, 2, 40, 8.4, 67.79, 0.0001, 43, 25.16, 15.88, 0.9999, 4, 42, 27.02, 15.77, 0.01034, 43, 17.18, 5.16, 0.98911, 44, 19.45, -54.77, 0.00054, 45, -46.46, -29.29, 0, 4, 42, 14.6, 10.51, 0.63158, 43, 4.66, 10.18, 0.33553, 44, 6.74, -50.25, 0.03286, 45, -50.38, -16.38, 4e-05, 2, 29, 197.18, 107.5, 0.928, 42, -2.64, 27.86, 0.072, 2, 29, 188.28, 121.96, 0.928, 44, 10.08, -9.39, 0.072, 2, 44, 27.43, -3.96, 0.40541, 45, -0.86, -5.51, 0.59459, 2, 42, -2.24, 74.6, 0, 45, 15.14, -6.42, 1, 3, 42, -20.05, 96.5, 0, 44, 39.35, 36.54, 0.00023, 45, 38.8, 8.97, 0.99977, 6, 29, 182.81, 152.6, 0, 30, -23.25, 150.09, 0, 40, -33.16, 110.37, 0, 44, 28.44, 15.74, 0.01723, 45, 15.59, 5.39, 0.98277, 47, -35.3, -117.41, 0, 4, 30, -26.64, 135.3, 0, 40, -37.17, 95.74, 0, 44, 15.17, 8.39, 0.80756, 45, 1.79, 11.69, 0.19244, 2, 29, 168.55, 130.96, 0.928, 44, 2.94, 11.09, 0.072, 2, 29, 159.36, 146.33, 0.96, 44, 7.69, 28.36, 0.04, 2, 29, 143.34, 150.35, 0.904, 46, 1.86, -10.87, 0.096, 2, 44, 8.62, 52.31, 0.00428, 46, 15.02, -9.55, 0.99572, 2, 44, 10.22, 67.01, 5e-05, 46, 25.85, 0.52, 0.99995, 3, 29, 112.04, 178.03, 0, 46, 27.94, 21.78, 0.99997, 47, 13.23, -59.97, 3e-05, 1, 46, 18.81, 7.12, 1, 3, 29, 128.99, 158.4, 4e-05, 46, 9.19, 3.86, 0.99511, 47, -11.08, -69.01, 0.00485, 2, 29, 120.21, 147.22, 0.928, 46, -2.42, 12.07, 0.072, 2, 29, 104.91, 166.98, 0.952, 46, 16.55, 28.35, 0.048, 2, 29, 90.01, 161.09, 0.968, 47, 5.04, -33.42, 0.032, 2, 29, 77.93, 150.23, 0.928, 47, -0.92, -18.3, 0.072, 3, 46, 8.71, 64.04, 0.04108, 47, 12.29, -13.54, 0.6354, 48, 0.97, -13.89, 0.32351, 3, 46, 14.35, 72.69, 0.00256, 47, 20.89, -7.83, 0.12923, 48, 7.47, -5.87, 0.86821, 2, 29, 59.71, 180.98, 0, 48, 21.47, -5.83, 1, 2, 29, 52.51, 181.04, 0, 48, 21.91, 1.36, 1, 1, 48, 6.86, 8.74, 1, 3, 29, 49.18, 156.74, 0.00647, 47, 15.22, 6.37, 0.67446, 48, -2.17, 5.99, 0.31907, 3, 29, 33.84, 151.31, 0.0506, 47, 15.48, 22.64, 0.94403, 48, -6.78, 21.6, 0.00536, 2, 29, 28.2, 139.05, 0.912, 47, 5.96, 32.2, 0.088, 1, 29, 17.47, 148.73, 1, 1, 29, 15.5, 165.99, 1, 1, 29, 5.41, 158.12, 1, 1, 29, 4.99, 141.77, 1, 1, 29, -9.31, 135.13, 1, 1, 29, -17.7, 115.39, 1, 1, 29, -19.52, 10.19, 1, 1, 29, -20.44, -112.95, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 0, 190]}}}}], "animations": {"anger": {"slots": {"h1": {"attachment": [{"name": "tw_00"}, {"time": 0.0333, "name": "tw_01"}, {"time": 0.0667, "name": "tw_02"}, {"time": 0.1, "name": "tw_03"}, {"time": 0.1333, "name": "tw_04"}, {"time": 0.1667, "name": "tw_05"}, {"time": 0.2, "name": "tw_06"}, {"time": 0.2333, "name": "tw_07"}, {"time": 0.2667, "name": "tw_08"}, {"time": 0.3, "name": "tw_09"}, {"time": 0.3333, "name": "tw_10"}, {"time": 0.3667, "name": "tw_11"}, {"time": 0.4, "name": "tw_12"}, {"time": 0.4333, "name": "tw_13"}, {"time": 0.4667, "name": "tw_14"}, {"time": 0.5, "name": "tw_00"}, {"time": 0.5333, "name": "tw_01"}, {"time": 0.5667, "name": "tw_02"}, {"time": 0.6, "name": "tw_03"}, {"time": 0.6333, "name": "tw_04"}, {"time": 0.6667, "name": "tw_05"}, {"time": 0.7, "name": "tw_06"}, {"time": 0.7333, "name": "tw_07"}, {"time": 0.7667, "name": "tw_08"}, {"time": 0.8, "name": "tw_09"}, {"time": 0.8333, "name": "tw_10"}, {"time": 0.8667, "name": "tw_11"}, {"time": 0.9, "name": "tw_12"}, {"time": 0.9333, "name": "tw_13"}, {"time": 0.9667, "name": "tw_14"}, {"time": 1, "name": "tw_15"}]}, "h2": {"attachment": [{"name": "tw_00"}, {"time": 0.0333, "name": "tw_01"}, {"time": 0.0667, "name": "tw_02"}, {"time": 0.1, "name": "tw_03"}, {"time": 0.1333, "name": "tw_04"}, {"time": 0.1667, "name": "tw_05"}, {"time": 0.2, "name": "tw_06"}, {"time": 0.2333, "name": "tw_07"}, {"time": 0.2667, "name": "tw_08"}, {"time": 0.3, "name": "tw_09"}, {"time": 0.3333, "name": "tw_10"}, {"time": 0.3667, "name": "tw_11"}, {"time": 0.4, "name": "tw_12"}, {"time": 0.4333, "name": "tw_13"}, {"time": 0.4667, "name": "tw_14"}, {"time": 0.5, "name": "tw_00"}, {"time": 0.5333, "name": "tw_01"}, {"time": 0.5667, "name": "tw_02"}, {"time": 0.6, "name": "tw_03"}, {"time": 0.6333, "name": "tw_04"}, {"time": 0.6667, "name": "tw_05"}, {"time": 0.7, "name": "tw_06"}, {"time": 0.7333, "name": "tw_07"}, {"time": 0.7667, "name": "tw_08"}, {"time": 0.8, "name": "tw_09"}, {"time": 0.8333, "name": "tw_10"}, {"time": 0.8667, "name": "tw_11"}, {"time": 0.9, "name": "tw_12"}, {"time": 0.9333, "name": "tw_13"}, {"time": 0.9667, "name": "tw_14"}, {"time": 1, "name": "tw_15"}]}}, "bones": {"s2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 10.24, "curve": "stepped"}, {"time": 0.5, "y": 10.24, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "s27": {"rotate": [{"angle": 0.31, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": -0.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 8.72, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 0.31}]}, "s47": {"rotate": [{"angle": -7.27, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": -12.33, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 12.66, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -7.27}]}, "s29": {"rotate": [{"angle": -7.23, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": -16.39, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 8.5, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -7.23}]}, "s37": {"rotate": [{"angle": 7.8, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 8.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -8.19, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 7.8}]}, "s38": {"rotate": [{"angle": 5.31, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 10.14, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -13.72, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 5.31}]}, "s39": {"rotate": [{"angle": 14.12, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 16.46, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -17.26, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 14.12}]}, "s40": {"rotate": [{"angle": 9.64, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 16.45, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -17.14, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 9.64}]}, "s41": {"rotate": [{"angle": 14.35, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 16.77, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -18.12, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 14.35}]}, "s42": {"rotate": [{"angle": 11.58, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 22.15, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -30.03, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 11.58}]}, "s33": {"rotate": [{"angle": -3.74, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": -10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 23.28, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -3.74}]}, "s30": {"rotate": [{"angle": -6.99, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": -8.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 9.5, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -6.99}]}, "s17": {"rotate": [{"angle": 8.37, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 10.11, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 1.5, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 8.37}], "translate": [{"x": -0.39, "y": -0.09, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -1.93, "y": -0.42, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "x": -0.39, "y": -0.09}]}, "s5": {"rotate": [{"angle": 6.47, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 17.58, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 6.47}]}, "zz": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "y": 15.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "y": 4.89, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "s14": {"rotate": [{"angle": -1.54, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -7.58, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -1.54}]}, "s15": {"rotate": [{"angle": -7.32, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -19.9, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -7.32}]}, "s3": {"rotate": [{"angle": 0.22, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.23, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 0.22}]}, "s8": {"rotate": [{"angle": -3.96, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -19.54, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -3.96}]}, "s4": {"rotate": [{"angle": 1.49, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 7.34, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 1.49}]}, "s6": {"rotate": [{"angle": 17.18, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 31.58, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 17.18}]}, "s7": {"rotate": [{"angle": -0.78, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.18, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -0.78}]}, "s1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 2.48, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "s26": {"translate": [{"y": -7.46, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -7.46}]}, "s10": {"rotate": [{"angle": 0.55, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 7.94, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 0.55}]}, "s11": {"rotate": [{"angle": 0.89, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 4.39, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 0.89}]}, "s12": {"rotate": [{"angle": 5.53, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 15.04, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 5.53}]}, "s13": {"rotate": [{"angle": -0.42, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -6.11, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -0.42}]}, "s9": {"rotate": [{"angle": -5.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -15.79, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -5.81}]}, "s16": {"rotate": [{"angle": 4.92, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 5.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 0.45, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 4.92}], "translate": [{"x": 0.01, "y": 0.16, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 0.11, "y": 2.37, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "x": 0.01, "y": 0.16}]}, "s19": {"rotate": [{"angle": -7.52, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 10.68, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -22.77, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": -7.52}]}, "s45": {"rotate": [{"angle": 7.53, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 15.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -24.48, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 7.53}]}, "s21": {"rotate": [{"angle": 4.5, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": 7.79, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -17.53, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": 4.5}]}, "s22": {"rotate": [{"angle": 0.54, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 9.11, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -14.18, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 0.54}]}, "s24": {"rotate": [{"angle": 2.69, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 7.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -16.4, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 2.69}], "translate": [{"x": 10.18, "y": -11.92, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "x": 10.4, "y": -11.05, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 9.31, "y": -15.34, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "x": 10.18, "y": -11.92}]}, "s20": {"rotate": [{"angle": -1.93, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": 34.13, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -16.22, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -1.93}]}, "s18": {"rotate": [{"angle": 6.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 9.19, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 2.15, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 6.6}], "translate": [{"x": -1.4, "y": -0.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -3.8, "y": -1.11, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "x": -1.4, "y": -0.41}]}, "s44": {"rotate": [{"angle": 9.59, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 11.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -19.56, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 9.59}]}, "s23": {"rotate": [{"angle": -6.54, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "angle": 18.65, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -21.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "angle": -6.54}]}, "s31": {"rotate": [{"angle": -7.63, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": -14.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 18.41, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -7.63}]}, "s46": {"rotate": [{"angle": -5.92, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": -7.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 17.74, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -5.92}]}, "s28": {"rotate": [{"angle": -10.11, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": -14.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 5.54, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -10.11}]}, "s34": {"rotate": [{"angle": -6.76, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": -10.91, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 20.98, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": -6.76}]}, "s32": {"rotate": [{"angle": -4.45, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": -5.88, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 14.66, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -4.45}]}, "s35": {"rotate": [{"angle": -2.63, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 14.23, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -2.63}]}, "s36": {"rotate": [{"angle": -2.65, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": -8.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 19.11, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -2.65}]}, "s43": {"rotate": [{"angle": 8.67, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": 14.15, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -27.95, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": 8.67}]}, "zz2": {"translate": [{"y": 25.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -13.33, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 25.45}]}, "s25": {"rotate": [{"angle": -13.14, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 9.91, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -32.46, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": -13.14}]}, "h2": {"rotate": [{"angle": 100.75}], "translate": [{"y": -156.11}], "scale": [{"x": -1}]}}, "deform": {"default": {"s1": {"s1": [{}, {"time": 0.5, "offset": 50, "vertices": [0.93499, 0, -3.07901, 0, -2.70502, 0, 1.49599, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.32301, 0, -5.88399, 0, -0.33199, 5.87463, -0.42544, -5.86856, -5.13599]}, {"time": 1}]}, "s2": {"s2": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3333, "offset": 98, "curve": "stepped", "vertices": [2.87189, -0.02684, -2.15389, 0.02013, -3.58983, 0.03355, 0.71798, -0.00671]}, {"time": 0.5, "offset": 98, "curve": 0.25, "c3": 0.75, "vertices": [2.87189, -0.02684, -2.15389, 0.02013, -3.58983, 0.03355, 0.71798, -0.00671]}, {"time": 1}]}}}}, "idle": {"bones": {"zz": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 7.84, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "s1": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 2.48, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "s2": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": 4.33, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "s3": {"rotate": [{"angle": 0.22, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 3.23, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 0.22}]}, "s4": {"rotate": [{"angle": 1.49, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 7.34, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 1.49}]}, "s5": {"rotate": [{"angle": 6.47, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 17.58, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 6.47}]}, "s6": {"rotate": [{"angle": 17.18, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 31.58, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": 17.18}]}, "s7": {"rotate": [{"angle": -0.78, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -11.18, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -0.78}]}, "s8": {"rotate": [{"angle": -3.96, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -19.54, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -3.96}]}, "s9": {"rotate": [{"angle": -5.81, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -15.79, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -5.81}]}, "s10": {"rotate": [{"angle": 0.55, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 7.94, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 0.55}]}, "s11": {"rotate": [{"angle": 0.89, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 4.39, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 0.89}]}, "s12": {"rotate": [{"angle": 5.53, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 15.04, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 5.53}]}, "s13": {"rotate": [{"angle": -0.42, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -6.11, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -0.42}]}, "s14": {"rotate": [{"angle": -1.54, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -7.58, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -1.54}]}, "s15": {"rotate": [{"angle": -7.32, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -19.9, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -7.32}]}, "s16": {"rotate": [{"angle": 4.92, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 5.25, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 0.45, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 4.92}], "translate": [{"x": 0.01, "y": 0.16, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 0.11, "y": 2.37, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "x": 0.01, "y": 0.16}]}, "s17": {"rotate": [{"angle": 8.37, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 10.11, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 1.5, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 8.37}], "translate": [{"x": -0.39, "y": -0.09, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": -1.93, "y": -0.42, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "x": -0.39, "y": -0.09}]}, "s18": {"rotate": [{"angle": 6.6, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 9.19, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 2.15, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 6.6}], "translate": [{"x": -1.4, "y": -0.41, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -3.8, "y": -1.11, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "x": -1.4, "y": -0.41}]}, "s19": {"rotate": [{"angle": -7.52, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 10.68, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -22.77, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": -7.52}]}, "s20": {"rotate": [{"angle": -1.93, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.3333, "angle": 34.13, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -16.22, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 1, "angle": -1.93}]}, "s21": {"rotate": [{"angle": 4.5, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": 7.79, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -17.53, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": 4.5}]}, "s22": {"rotate": [{"angle": 0.54, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": 9.11, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": -14.18, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": 0.54}]}, "s23": {"rotate": [{"angle": -6.54, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.3, "angle": 18.65, "curve": 0.25, "c3": 0.75}, {"time": 0.8, "angle": -21.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 1, "angle": -6.54}]}, "s24": {"rotate": [{"angle": 2.69, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 7.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -16.4, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 2.69}], "translate": [{"x": 10.18, "y": -11.92, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "x": 10.4, "y": -11.05, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 9.31, "y": -15.34, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "x": 10.18, "y": -11.92}]}, "s25": {"rotate": [{"angle": -13.14, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.2667, "angle": 9.91, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -32.46, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 1, "angle": -13.14}]}, "s26": {"translate": [{"y": -7.46, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -7.46}]}, "s27": {"rotate": [{"angle": 0.31, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": -0.31, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 8.72, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 0.31}]}, "s28": {"rotate": [{"angle": -10.11, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": -14.09, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 5.54, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -10.11}]}, "s29": {"rotate": [{"angle": -7.23, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.2, "angle": -16.39, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 8.5, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "angle": -7.23}]}, "s30": {"rotate": [{"angle": -6.99, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": -8.22, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 9.5, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -6.99}]}, "s31": {"rotate": [{"angle": -7.63, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": -14.25, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 18.41, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -7.63}]}, "s32": {"rotate": [{"angle": -4.45, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": -5.88, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 14.66, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -4.45}]}, "s33": {"rotate": [{"angle": -3.74, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": -10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 23.28, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -3.74}]}, "s34": {"rotate": [{"angle": -6.76, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": -10.91, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 20.98, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": -6.76}]}, "s35": {"rotate": [{"angle": -2.63, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": -3.89, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 14.23, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -2.63}]}, "s36": {"rotate": [{"angle": -2.65, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": -8.18, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 19.11, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -2.65}]}, "s37": {"rotate": [{"angle": 7.8, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 8.99, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -8.19, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 7.8}]}, "s38": {"rotate": [{"angle": 5.31, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 10.14, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -13.72, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 5.31}]}, "s39": {"rotate": [{"angle": 14.12, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 16.46, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -17.26, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 14.12}]}, "s40": {"rotate": [{"angle": 9.64, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 16.45, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -17.14, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 9.64}]}, "s41": {"rotate": [{"angle": 14.35, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 16.77, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -18.12, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 14.35}]}, "s42": {"rotate": [{"angle": 11.58, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 22.15, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -30.03, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 11.58}]}, "s43": {"rotate": [{"angle": 8.67, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.1, "angle": 14.15, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -27.95, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 1, "angle": 8.67}]}, "s44": {"rotate": [{"angle": 9.59, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": 11.76, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -19.56, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": 9.59}]}, "s45": {"rotate": [{"angle": 7.53, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": 15.66, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -24.48, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": 7.53}]}, "s46": {"rotate": [{"angle": -5.92, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "angle": -7.69, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 17.74, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "angle": -5.92}]}, "s47": {"rotate": [{"angle": -7.27, "curve": 0.38, "c2": 0.59, "c3": 0.727}, {"time": 0.1333, "angle": -12.33, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": 12.66, "curve": 0.242, "c3": 0.663, "c4": 0.65}, {"time": 1, "angle": -7.27}]}, "zz2": {"translate": [{"y": 25.45, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "y": -13.33, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": 25.45}]}}, "deform": {"default": {"s1": {"s1": [{}, {"time": 0.5, "offset": 50, "vertices": [0.93499, 0, -3.07901, 0, -2.70502, 0, 1.49599, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -5.32301, 0, -5.88399, 0, -0.33199, 5.87463, -0.42544, -5.86856, -5.13599]}, {"time": 1}]}, "s2": {"s2": [{}, {"time": 0.5, "offset": 98, "vertices": [2.87189, -0.02684, -2.15389, 0.02013, -3.58983, 0.03355, 0.71798, -0.00671]}, {"time": 1}]}}}}}}, [0]]], 0, 0, [0], [-1], [0]]