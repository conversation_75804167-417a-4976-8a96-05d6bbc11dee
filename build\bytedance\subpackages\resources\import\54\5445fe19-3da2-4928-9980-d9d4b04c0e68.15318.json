[1, ["3f/A7ne85PSrwS2GmHd3FL"], 0, [["sp.SkeletonData", ["_name", "_native", "_atlasText", "textureNames", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "long_red_s", ".bin", "\nlong_red_s.png\nsize: 475,153\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\ne1\n  rotate: false\n  xy: 383, 21\n  size: 51, 47\n  orig: 51, 47\n  offset: 0, 0\n  index: -1\nee1\n  rotate: false\n  xy: 449, 110\n  size: 12, 12\n  orig: 12, 12\n  offset: 0, 0\n  index: -1\nf002\n  rotate: true\n  xy: 436, 40\n  size: 28, 37\n  orig: 29, 39\n  offset: 0, 1\n  index: -1\nf003\n  rotate: true\n  xy: 292, 2\n  size: 34, 43\n  orig: 35, 45\n  offset: 1, 1\n  index: -1\nf004\n  rotate: true\n  xy: 337, 3\n  size: 33, 40\n  orig: 34, 42\n  offset: 0, 1\n  index: -1\nf005\n  rotate: false\n  xy: 436, 13\n  size: 27, 25\n  orig: 28, 27\n  offset: 0, 1\n  index: -1\nf006\n  rotate: true\n  xy: 449, 124\n  size: 27, 18\n  orig: 29, 21\n  offset: 1, 2\n  index: -1\nh1\n  rotate: false\n  xy: 2, 5\n  size: 288, 146\n  orig: 288, 146\n  offset: 0, 0\n  index: -1\nm1\n  rotate: false\n  xy: 292, 70\n  size: 155, 81\n  orig: 155, 81\n  offset: 0, 0\n  index: -1\nmm1\n  rotate: false\n  xy: 292, 38\n  size: 89, 30\n  orig: 89, 30\n  offset: 0, 0\n  index: -1\n", ["long_red_s.png"], [0]], -1], 0, 0, [0], [-1], [0]]