[1, ["ecpdLyjvZBwrvm+cedCcQy", "ef1j2vS8ROabD/02fuTola", "29FYIk+N1GYaeWH/q1NxQO", "1daaY1VXJHTZOf6YmkFtkv", "a2MjXRFdtLlYQ5ouAFv/+R", "7a/QZLET9IDreTiBfRn2PD", "e4Ywdam8xC/LIZT6mbTitr"], ["node", "_N$file", "_spriteFrame", "_N$disabledSprite", "_N$skeletonData", "root", "data", "_parent"], [["cc.Node", ["_name", "_active", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color"], -1, 4, 5, 9, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$cacheMode", "_fontSize", "_lineHeight", "_N$overflow", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$disabledSprite"], 2, 1, 9, 5, 5, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 2, 4, 5, 5, 7], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 12, 4, 5, 7], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials", "_N$skeletonData"], -1, 1, 3, 6], ["7deaaSyBo5Gcowd3Z2Buxok", ["node", "nodeArr", "labelArr", "imgArr"], 3, 1, 2, 2, 2]], [[4, 0, 1, 2, 2], [0, 0, 7, 6, 4, 5, 9, 2], [9, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 8, 5, 9, 10, 11, 8], [10, 0, 1, 2, 3, 4], [3, 0, 1, 2, 3, 4], [1, 1, 0, 2, 3, 4, 3], [5, 0, 1, 2, 3, 4, 5, 2], [6, 0, 2], [0, 0, 2, 8, 6, 4, 5, 3], [0, 0, 7, 6, 4, 5, 2], [0, 0, 7, 8, 6, 4, 5, 2], [0, 0, 3, 7, 6, 4, 10, 5, 3], [0, 0, 7, 8, 4, 5, 2], [0, 0, 8, 6, 4, 5, 9, 2], [0, 0, 7, 8, 6, 4, 5, 9, 2], [0, 0, 1, 7, 8, 6, 4, 5, 9, 3], [0, 0, 7, 8, 4, 5, 9, 2], [0, 0, 1, 7, 6, 4, 5, 9, 3], [7, 0, 1, 2, 3, 4, 5, 6, 7, 2], [8, 0, 1, 2, 3, 4, 5, 6, 3], [3, 0, 3, 2], [4, 1, 2, 1], [1, 0, 2, 3, 4, 2], [1, 1, 2, 3, 2], [2, 0, 6, 7, 1, 2, 3, 4, 5, 9, 10, 9], [2, 0, 6, 1, 2, 3, 4, 5, 9, 10, 11, 8], [5, 1, 2, 1], [11, 0, 1, 2, 3, 4, 4], [12, 0, 1, 2, 3, 4, 5, 6, 5], [13, 0, 1, 2, 3, 1]], [[8, "M30_Pop_GameEnd"], [9, "M30_Pop_GameEnd", 1, [-6, -7], [[30, -5, [-4], [-3], [-2]]], [22, -1, 0], [5, 750, 1334]], [11, "bg", 1, [-10, -11, -12], [[5, 45, 750, 1334, -8], [27, -9, [[4, "7deaaSyBo5Gcowd3Z2Buxok", "onBtn", "BackToMain", 1]]]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [14, "New Layout", [-14, -15, -16], [[28, 1, 2, 40, -13, [5, 536, 200]]], [0, "f0K2sG0ZJAzaauAIbLYkZi", 1, 0], [5, 536, 200], [0, -197.518, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "tryPlayTips", false, 3, [-19, -20], [[7, 3, -17, [[4, "7deaaSyBo5Gcowd3Z2Buxok", "onBtn", "BackToMain", 1]], [4, 4293322470], [4, 3363338360], 13], [6, 1, 0, -18, [14], 15]], [0, "dbB8+cU95Js4gFCYwjpbmQ", 1, 0], [5, 248, 100], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [15, "btn_norget", 3, [-23], [[7, 3, -21, [[4, "7deaaSyBo5Gcowd3Z2Buxok", "onBtn", "Replay", 1]], [4, 4293322470], [4, 3363338360], 6], [6, 1, 0, -22, [7], 8]], [0, "51X/rzr0hPrpBa8t5zA0XV", 1, 0], [5, 248, 100], [0, 50, 0, 0, 0, 0, 1, 1, 1, 0]], [12, "mask", 140, 2, [[23, 0, -24, [0], 1], [5, 45, 100, 100, -25]], [0, "7drJD7BltN34VFsLgXHyGQ", 1, 0], [4, 4278190080], [5, 750, 1334]], [13, "content", 2, [-26, 3], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 679, 1126]], [19, "title_zhua<PERSON><PERSON>", 7, [-28], [-27], [0, "40Pj7EgxlMGabfwMIymoC3", 1, 0], [5, 577, 247], [0, 0.5, 0], [0, 116.117, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "Label", false, 8, [[-29, [2, 6, -30, [4, 4278190080]]], 1, 4], [0, "32D8nkG5FB84AWqjJGh/R+", 1, 0], [5, 332, 125.4], [0, 77.721, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 5, [[3, "再次挑战", false, false, 1, 1, 2, 1, -31, [4], 5], [2, 3, -32, [4, 3573547008]]], [0, "71oGKaoedCvqcD5CKQPbNR", 1, 0], [5, 136, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "tips", 4, [[26, "通关主线第2关后可以继续", 32, false, false, 1, 1, 1, -33, [9], 10], [2, 3, -34, [4, 3573547008]]], [0, "f1pcpq/75MYrhY0c1UiL3r", 1, 0], [5, 375.56, 56.4], [0, -74.018, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 4, [[3, "开始主线", false, false, 1, 1, 2, 1, -35, [11], 12], [2, 3, -36, [4, 3573547008]]], [0, "ea/L5ZxWhA9IX1bFiT7XFZ", 1, 0], [5, 136, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "maskbg", 1, [[21, 45, -37]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [5, 750, 1334]], [25, "游戏失败", 80, 90, false, false, 1, 1, 1, 9, [2]], [24, 1, 8, [3]], [17, "close", 3, [-38], [0, "e7A9Ywz3hHG6J33oqxNDSZ", 1, 0], [5, 248, 100], [0, -50, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 16, [[3, "点击关闭", false, false, 1, 1, 2, 1, -39, [16], 17]], [0, "4fnLaXlz5GtJUUHww+c4xs", 1, 0], [5, 136, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "effect_ribbons", false, 2, [[29, "default", "animation", 0, "animation", -40, [18], 19]], [0, "c60DQJ3S5BYKleDWdDZmFO", 1, 0], [5, 536.40966796875, 33.47761917114258], [0, 646.266, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 5, 1, 0, -1, 15, 0, -1, 14, 0, -1, 3, 0, 0, 1, 0, -1, 13, 0, -2, 2, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 7, 0, -3, 18, 0, 0, 3, 0, -1, 5, 0, -2, 4, 0, -3, 16, 0, 0, 4, 0, 0, 4, 0, -1, 11, 0, -2, 12, 0, 0, 5, 0, 0, 5, 0, -1, 10, 0, 0, 6, 0, 0, 6, 0, -1, 8, 0, -1, 15, 0, -1, 9, 0, -1, 14, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, -1, 17, 0, 0, 17, 0, 0, 18, 0, 6, 1, 3, 7, 7, 40], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14], [-1, 2, -1, -1, -1, 1, 3, -1, 2, -1, 1, -1, 1, 3, -1, 2, -1, 1, -1, 4, 1], [0, 4, 0, 0, 0, 1, 2, 0, 3, 0, 1, 0, 1, 2, 0, 3, 0, 1, 5, 6, 1]]