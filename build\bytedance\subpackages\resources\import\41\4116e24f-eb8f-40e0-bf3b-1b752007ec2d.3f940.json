[1, ["54F9Hwcp5MxKvwbJ6CPQ4f"], ["node", "root", "data"], [["cc.CurveRange", ["mode", "constantMax", "constantMin", "constant"], -1], ["cc.GradientRange", ["_mode", "colorMin", "colorMax", "gradient", "color"], 2, 5, 5, 4, 5], ["cc.<PERSON><PERSON><PERSON>", ["time", "alpha"], 1], ["cc.Node", ["_name", "_is3DNode", "_prefab", "_trs", "_children", "_parent", "_components", "_eulerAngles"], 1, 4, 7, 2, 1, 9, 5], ["cc.ParticleSystem3D", ["_capacity", "_renderMode", "_velocityScale", "_lengthScale", "node", "_materials", "startDelay", "startLifetime", "startColor", "startSize", "startSpeed", "startRotation", "gravityModifier", "rateOverTime", "rateOverDistance", "_shapeModule", "_colorOverLifetimeModule", "_limitVelocityOvertimeModule"], -1, 1, 3, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.ShapeModule", ["enable", "emitFrom", "_angle", "radius", "arcSpeed", "_scale"], -1, 4, 5], ["cc.<PERSON><PERSON>", ["time"], 2], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.ColorOvertimeModule", ["enable", "color"], 2, 4], ["cc.Gradient", ["colorKeys", "alphaKeys"], 3, 9, 9], ["cc.LimitVelocityOvertimeModule", ["enable", "dampen", "limit", "limitX", "limitY", "limitZ"], 1, 4, 4, 4, 4]], [[0, 1], [0, 0, 2, 1, 4], [6, 0, 2], [3, 0, 1, 5, 6, 2, 3, 7, 3], [0, 0, 1, 3], [0, 3, 2], [1, 0, 3, 2], [9, 0, 1, 2], [10, 0, 1, 1], [6, 1], [2, 1], [2, 1, 0, 3], [2, 0, 2], [11, 0, 1, 2, 3, 4, 5, 3], [7, 0, 1, 2, 2], [8, 0, 2], [3, 0, 4, 2, 3, 2], [4, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 1], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 5], [1, 0, 1, 2, 2], [1, 4, 1], [5, 0, 1, 2, 4, 5, 4], [5, 0, 1, 3, 2, 4, 5, 5], [7, 1, 2, 1]], [[15, "FX_shexian_2"], [16, "FX_shexian_2", [-2, -3], [23, -1, 0], [-345.339, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "LIZI", true, 1, [[17, -4, [0], [0], [1, 3, 0.5, 1], [19, 2, [4, 4294935014], [4, 4294914815]], [1, 3, 0.1, 0.4], [1, 3, 2, 7.5], [4, 3, 360], [0], [5, 10], [0], [21, true, 0, 0, [0], [1, 1.2, 0, 1]], [7, true, [6, 1, [8, [[9], [2, 0.6409090909090909], [2, 0.6409090909090909], [2, 0.6409090909090909]], [[10], [11, 255, 0.37727272727272726], [12, 0.9954545454545454]]]]], [13, true, 0.4, [0], [0], [0], [0]]]], [14, "84h0qpk8xAt4UTwhvQQfzs", 1, 0], [0, -17.84, 0, 0.7071067811865475, 0, 0, 0.7071067811865476, 10, 10, 10], [1, 90, 0, 0]], [3, "lashen", true, 1, [[18, 10, 1, 0, 6, -5, [1], [0], [1, 3, 0.5, 1], [20, [4, 4294915059]], [1, 3, 0.1, 0.3], [1, 3, 1, 5.5], [4, 3, 360], [0], [5, 8], [0], [22, true, 0, 0.8, 0, [0], [1, 1.2, 0, 1]], [7, true, [6, 1, [8, [[9], [2, 0.6409090909090909], [2, 0.6409090909090909], [2, 0.6409090909090909]], [[10], [11, 255, 0.37727272727272726], [12, 0.9954545454545454]]]]], [13, true, 0.4, [0], [0], [0], [0]]]], [14, "9d+Bh6WstECZXpoQTsXzTJ", 1, 0], [0, -12.246, 0, 0.7071067811865475, 0, 0, 0.7071067811865476, 10, 10, 10], [1, 90, 0, 0]]], 0, [0, 1, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 3, 0, 2, 1, 5], [0, 0], [-1, -1], [0, 0]]