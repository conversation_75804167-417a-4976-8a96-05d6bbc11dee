[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2MjXRFdtLlYQ5ouAFv/+R", "7a/QZLET9IDreTiBfRn2PD", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "ffDpHHVcZAhqOfy25Fe4w7"], ["node", "_spriteFrame", "_parent", "_N$normalSprite", "_N$disabledSprite", "root", "_N$background", "_N$placeholderLabel", "_N$textLabel", "data"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_obj<PERSON><PERSON>s", "_active", "_parent", "_prefab", "_contentSize", "_components", "_trs", "_color", "_children", "_anchorPoint"], -2, 1, 4, 5, 9, 7, 5, 12, 5], ["cc.Label", ["_N$verticalAlign", "_fontSize", "_N$horizontalAlign", "_string", "_lineHeight", "_enableWrapText", "_N$overflow", "_N$cacheMode", "node", "_materials"], -5, 1, 3], ["cc.Node", ["_name", "_groupIndex", "_components", "_active", "_prefab", "_parent", "_children", "_contentSize", "_trs"], -1, 4, 1, 2, 5, 7], ["cc.Node", ["_name", "_groupIndex", "_active", "_obj<PERSON><PERSON>s", "_children", "_components", "_prefab", "_contentSize", "_parent", "_trs"], -1, 2, 9, 4, 5, 1, 7], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "alignMode", "_originalHeight", "_right", "_left", "node"], -3, 1], ["cc.Node", ["_name", "_groupIndex", "_obj<PERSON><PERSON>s", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint", "_children", "_color"], -1, 1, 12, 4, 5, 7, 5, 2, 5], ["cc.RigidBody", ["_type", "_allowSleep", "_gravityScale", "_fixedRotation", "node"], -1, 1], ["cc.PhysicsCircleCollider", ["_radius", "node", "_offset"], 2, 1, 5], ["cc.<PERSON>", ["_radius", "tag", "node", "_offset"], 1, 1, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_groupIndex", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 9, 9, 4, 5, 7], ["cc.<PERSON>", ["tag", "node", "_offset", "_size"], 2, 1, 5, 5], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Layout", ["_N$layoutType", "_N$paddingTop", "_N$spacingY", "node"], 0, 1], ["cc.PhysicsPolygonCollider", ["node", "points"], 3, 1, 12], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -2, 1, 3], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.EditBox", ["max<PERSON><PERSON><PERSON>", "_N$inputMode", "node", "_N$textLabel", "_N$placeholderLabel", "_N$background"], 1, 1, 1, 1, 1]], [[10, 0, 1, 2, 2], [4, 2, 3, 1], [0, 0, 1, 5, 8, 6, 7, 9, 3], [7, 0, 3, 4, 3], [8, 0, 1, 2], [9, 0, 2, 2], [0, 0, 2, 1, 5, 8, 6, 7, 9, 4], [4, 0, 2, 3, 4, 2], [0, 0, 1, 5, 8, 6, 7, 12, 9, 3], [0, 0, 1, 5, 8, 6, 10, 7, 9, 3], [14, 0, 1, 2, 2], [17, 0, 1, 2, 3, 4, 5, 6, 6], [5, 0, 1, 6, 3], [0, 0, 1, 5, 8, 6, 7, 3], [0, 0, 1, 5, 8, 6, 9, 3], [3, 0, 1, 8, 4, 5, 6, 7, 9, 3], [1, 3, 2, 0, 8, 9, 4], [1, 1, 4, 2, 0, 8, 9, 5], [7, 0, 4, 2], [8, 0, 1, 2, 2], [9, 0, 2, 3, 2], [5, 2, 0, 1, 3, 6, 5], [5, 2, 0, 5, 1, 3, 6, 6], [11, 0, 2], [2, 0, 1, 2, 6, 4, 7, 4], [2, 0, 5, 4, 2], [2, 0, 1, 5, 4, 8, 3], [2, 0, 3, 1, 5, 6, 4, 4], [2, 0, 1, 5, 4, 3], [2, 0, 1, 5, 4, 7, 8, 3], [0, 0, 1, 5, 11, 6, 7, 3], [0, 0, 1, 5, 8, 6, 10, 7, 3], [0, 0, 2, 1, 5, 8, 6, 10, 7, 9, 4], [0, 0, 3, 1, 5, 8, 6, 10, 7, 9, 4], [0, 0, 4, 1, 5, 8, 6, 7, 9, 4], [0, 0, 3, 1, 5, 8, 6, 10, 7, 4], [0, 0, 4, 1, 5, 8, 6, 10, 7, 9, 4], [3, 0, 1, 4, 5, 6, 7, 9, 3], [3, 0, 2, 1, 8, 4, 5, 6, 7, 4], [3, 0, 2, 1, 8, 4, 5, 6, 7, 9, 4], [3, 0, 3, 1, 4, 5, 6, 7, 4], [12, 0, 1, 2, 3, 4, 5, 6, 7, 3], [6, 0, 1, 4, 10, 5, 6, 7, 8, 3], [6, 0, 2, 1, 4, 5, 6, 7, 4], [6, 0, 2, 3, 1, 4, 5, 6, 7, 9, 8, 5], [6, 0, 2, 1, 4, 5, 6, 11, 7, 9, 8, 4], [4, 0, 2, 3, 2], [4, 1, 0, 2, 3, 3], [4, 1, 0, 2, 3, 4, 3], [10, 1, 2, 1], [13, 0, 1, 2, 3, 2], [1, 1, 2, 0, 8, 9, 4], [1, 1, 4, 5, 0, 6, 8, 6], [1, 3, 1, 4, 5, 0, 6, 8, 9, 7], [1, 3, 1, 5, 2, 0, 6, 7, 8, 9, 8], [1, 3, 1, 4, 2, 0, 8, 9, 6], [15, 0, 1, 2, 3, 4], [7, 1, 2, 4, 3], [16, 0, 1, 1], [8, 1, 1], [9, 1, 0, 2, 3], [5, 0, 4, 1, 6, 4], [5, 0, 6, 2], [18, 0, 1], [19, 0, 1, 2, 3, 4, 5, 6, 7, 2], [20, 0, 1, 2, 3], [21, 0, 1, 2, 3, 4, 5, 3]], [[23, "zqddn_zhb_level-29487"], [24, "zqddn_zhb_level-23270", 2, [null], [-2, -3], [49, -1, 0], [5, 750, 1334]], [37, "lineNode", 2, [-5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24], [[56, 2, 10, 20, -4]], [0, "70obw1p6xKNZBN39my5fGO", 1, 0], [5, 4, 890], [0, -4.321, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "game", 2, 1, [[-25, -26, -27, -28, -29, -30, -31, [25, "nodeList", -32, [0, "f2ZDvoGDtEIYv+foySh8Dk", 1, 0]], 2, -33, -34, -35, -36, -37, -38, [28, "<PERSON><PERSON><PERSON>er", 2, -39, [0, "40JLQQroBNqbj6ugs/UtNd", 1, 0]], -40, [29, "autoTouch", 2, -41, [0, "c8ib4SJdZCB5A4j9zfiAIJ", 1, 0], [5, 200, 500], [-264.251, 744.513, 0, 0, 0, 0, 1, 1, 1, 1]], -42, -43], 1, 1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 1, 1, 1, 4, 1, 4, 1, 1], [0, "40PrZFtMJC9ZD3q8BzBgy7", 1, 0], [5, 750, 960]], [27, "fruitIconParentNode", false, 3, 3, [-44, -45, -46, -47, -48, -49, -50, -51, -52, -53, -54, -55], [0, "44UXJ4+XpJlaTAA0/BX78M", 1, 0]], [38, "DifficultyLevel", false, 2, 3, [-57, -58, -59, -60, -61], [[62, 45, -56]], [0, "3484nczZdMvpbO5HwHhUAi", 1, 0], [5, 750, 960]], [39, "adjustBox", false, 2, 3, [-66, -67, -68, -69], [[66, 8, 6, -65, -64, -63, -62]], [0, "33qtDAV/dB8o8sbdSWeWcx", 1, 0], [5, 160, 40], [291.171, 564.862, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "fruitItemNode", 3, 3, [-73], [[[57, false, 10, -70], [4, 41, -71], null, [60, 1, 43, -72]], 4, 4, 0, 4], [0, "08IQYPya9NJKnBEM9S2Pxl", 1, 0], [5, 84, 91], [-1.706, 506.281, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "1=fruit1", 3, 4, [[1, -74, [29]], [3, 0, true, -75], [4, 26, -76], [5, 27, -77]], [0, "3dfuX0+nVNDbIHp1ztI64l", 1, 0], [5, 68, 68], [-1084.456, 706.619, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "2=fruit2", 3, 4, [[1, -78, [30]], [3, 0, true, -79], [4, 40, -80], [5, 41, -81]], [0, "fc3QoXeyxHNq5Q2cQyORHO", 1, 0], [5, 98, 98], [-1084.456, 594.637, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "3=fruit3", 3, 4, [[1, -82, [31]], [3, 0, true, -83], [59, -84], [5, 51, -85]], [0, "95nv8JXIFPyIys/t3zbsC0", 1, 0], [5, 118, 118], [-1084.456, 445.978, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "4=fruit4", 3, 4, [[1, -86, [32]], [3, 0, true, -87], [4, 60, -88], [5, 61, -89]], [0, "77UQxmljJAK55fhVab6GpN", 1, 0], [5, 138, 138], [-1084.456, 301.642, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "5=fruit5", 3, 4, [[1, -90, [33]], [3, 0, true, -91], [4, 75, -92], [5, 76, -93]], [0, "40WIxluABBC4iQWSxFpBtg", 1, 0], [5, 168, 168], [-1084.456, 102.47, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "6=fruit6", 3, 4, [[1, -94, [34]], [18, 0, -95], [4, 91, -96], [5, 92, -97]], [0, "5eA/+j971FjJB57G8gZoaF", 1, 0], [5, 198, 198], [-1084.456, -106.208, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7=fruit7", 3, 4, [[1, -98, [35]], [3, 0, true, -99], [4, 100, -100], [5, 101, -101]], [0, "79Wm49P+5Nh7txB+Rg4RI3", 1, 0], [5, 218, 218], [-1084.456, -325.192, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "8=fruit8", 3, 4, [[1, -102, [36]], [3, 0, true, -103], [4, 110, -104], [5, 111, -105]], [0, "857k7KSqtHLJADz/rypw8d", 1, 0], [5, 238, 238], [-1084.456, -650.529, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "9=fruit9", 3, 4, [[1, -106, [37]], [3, 0, true, -107], [4, 125, -108], [5, 126, -109]], [0, "a5wYql9OVLYb9yOsGyzCbJ", 1, 0], [5, 267, 268], [-1084.456, -1041.268, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "10=fruit10", 3, 4, [[1, -110, [38]], [3, 0, true, -111], [19, 150, -112, [0, 0, -5]], [20, 151, -113, [0, 0, -5]]], [0, "2bOkXLpSVJraGBBA+hYpAL", 1, 0], [5, 317, 318], [-1084.456, -1469.82, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "22=fruit11", 3, 4, [[1, -114, [39]], [3, 0, true, -115], [4, 200, -116], [5, 201, -117]], [0, "2ayXftXkFHdohunpJ/etlC", 1, 0], [5, 418, 418], [-1084.456, -1887.854, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "11=fruit12", 3, 4, [[1, -118, [40]], [3, 0, true, -119], [19, 200, -120, [0, 0, -3]], [20, 201, -121, [0, 0, -3]]], [0, "71IWUTtd5FB6nmMjc20Y/B", 1, 0], [5, 417, 418], [-1084.456, -2390.322, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "Background", 512, 2, [-124], [[48, 1, 0, -122, [56], 57], [21, 0, 45, 100, 40, -123]], [0, "7cGI2gH1JFObyFS06JlHK7", 1, 0], [5, 100, 40]], [41, "boxNode", 8, 3, [[26, "fruitParentNode", 8, -127, [0, "02Yu58QQJCE7xX7gt6sWDu", 1, 0], [0, 59.733, 0, 0, 0, 0, 1, 1, 1, 1]]], [[18, 0, -125], [58, -126, [[[0, -395, -401.215], [0, 395, -406], [0, 393, 717], [0, 374.8, 416.8], [0, 377.4, -368.7], [0, 374.8, -375.3], [0, 367.9, -378.5], [0, -360.3, -378.5], [0, -373.4, -377.6], [0, -377, -368.2], [0, -376.4, 412.3], [0, -394.6, 714.5]], 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8]]], [0, "2c4CFNwYpLxoF8ucVqqdtz", 1, 0], [5, 730, 834], [0, -59.733, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "17=deadLine", 2, 3, [[1, -128, [2]], [50, 2, -129, [0, 0, 5], [5, 736, 4]]], [0, "84w4ZUnftB46uziDwC+2oy", 1, 0], [5, 736, 4], [0, 460.697, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "targetScoreText", 2, 3, [[51, 35, 1, 1, -130, [4]], [10, 2, -131, [4, 4278190080]]], [0, "14WXFVaMpCjr1LsCBB3AMW", 1, 0], [5, 4, 54.4], [0, 0, 0.5], [-364.224, 497.372, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "13", 2, 3, [-133], [[1, -132, [6]]], [0, "fe5yJpKi9DsJjYWCwBHBYB", 1, 0], [5, 203, 76], [-262.691, 513.867, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "scoreText", 2, 24, [[17, 50, 55, 1, 1, -134, [5]], [10, 3, -135, [4, 4280700574]]], [0, "67CjPZ1zFDELodi+GjgnDd", 1, 0], [4, 4279565567], [5, 6, 75.3]], [8, "maxScoreText", 2, 3, [[17, 32, 38, 1, 1, -136, [7]], [10, 3, -137, [4, 4278190080]]], [0, "032h5cLnJPELzVRGZg+0FJ", 1, 0], [5, 6, 53.88], [0, 1, 0.5], [363.09, 492.672, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "topBar", 2, 5, [[7, 0, -138, [44], 45], [12, 41, 750, -139]], [0, "dfaOGySgxHsLDe9Ja+oqN1", 1, 0], [4, 4294954633], [5, 750, 115], [0, 422.5, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "butt<PERSON><PERSON><PERSON>", 2, 5, [[7, 0, -140, [46], 47], [12, 44, 750, -141]], [0, "39QgnQMnxIXL/QEUomSUOa", 1, 0], [4, 4294954633], [5, 750, 190], [0, -385, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "titleBar", 68, 2, 5, [[7, 0, -142, [48], 49], [61, 9, 325, 100, -143]], [0, "337zlaVaNJ6K31Szdasl/m", 1, 0], [4, 4279571733], [5, 100, 170], [-325, 395, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "forbiddenBar", 512, 2, 5, [[7, 0, -144, [50], 51], [12, 44, 750, -145]], [0, "63D/kIU7hPn6qKNn6Axaye", 1, 0], [4, 4278190335], [5, 750, 30], [0, -465, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "BACKGROUND_SPRITE", 512, 2, 6, [[-146, [21, 0, 45, 160, 40, -147]], 1, 4], [0, "49w0CDtMpG9opl5jBsZ+rO", 1, 0], [5, 160, 40]], [44, "TEXT_LABEL", 512, false, 2, 6, [[-148, [22, 0, 45, 2, 158, 40, -149]], 1, 4], [0, "4aC3/9JiZLF6t307TIQmHY", 1, 0], [5, 158, 40], [0, 0, 1], [-78, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [45, "PLACEHOLDER_LABEL", 512, 2, 6, [[-150, [22, 0, 45, 2, 158, 40, -151]], 1, 4], [0, "95hNQmYRFLY6gDwe4hik5a", 1, 0], [4, 4290493371], [5, 158, 40], [0, 0, 1], [-78, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "adjustBtn2", 2, 6, [20], [[64, 2, -152, [[65, "2130cW4BEJFH5pDoURHHvhZ", "adjustMakeSureBtn", 1]], [4, 4293322470], [4, 3363338360], 20, 58, 59]], [0, "28nnAAh3BB/pso7j6ALPhx", 1, 0], [5, 100, 40], [0, 43.184, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "15", 2, 3, [[46, 0, -153, [0]]], [0, "88kFL4IdJB3I7APdp2sGj/", 1, 0], [5, 750, 1800]], [2, "18", 8, 3, [[1, -154, [1]]], [0, "8aCSrgm9dEeJsz0GKqEAch", 1, 0], [5, 750, 46], [0, -461.479, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "lbTime", 2, 3, [[16, "00:00", 1, 1, -155, [3]]], [0, "8bt2/98UlKm65Bk0o2jrIC", 1, 0], [5, 100.1, 50.4], [0, 1, 0.5], [-249.337, 391.526, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "f23270.30", 2, 2, [[1, -156, [8]]], [0, "9f8cM7VshKubbLm0wjHPKZ", 1, 0], [5, 19, 19], [0, 425.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "f23270.30", 2, 2, [[1, -157, [9]]], [0, "b8GtsArP1N2JfBeox4IlRk", 1, 0], [5, 19, 19], [0, 386.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "f23270.30", 2, 2, [[1, -158, [10]]], [0, "f1rxsYldRBzJyp8zjGk9qP", 1, 0], [5, 19, 19], [0, 347.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "f23270.30", 2, 2, [[1, -159, [11]]], [0, "68wxaB7jpIEq1qh2X7zDC9", 1, 0], [5, 19, 19], [0, 308.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "f23270.30", 2, 2, [[1, -160, [12]]], [0, "f0YrPupKtBqocTSPRIjENi", 1, 0], [5, 19, 19], [0, 269.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "f23270.30", 2, 2, [[1, -161, [13]]], [0, "e95YY1t9xFbaxniM00msxs", 1, 0], [5, 19, 19], [0, 230.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "f23270.30", 2, 2, [[1, -162, [14]]], [0, "4egLP6Mg5LOYBemQVaZmhS", 1, 0], [5, 19, 19], [0, 191.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "f23270.30", 2, 2, [[1, -163, [15]]], [0, "19LWV2n+NNkKi4PlbdLhkr", 1, 0], [5, 19, 19], [0, 152.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "f23270.30", 2, 2, [[1, -164, [16]]], [0, "338MrNPBBDHbUOoqhjJCQ7", 1, 0], [5, 19, 19], [0, 113.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "f23270.30", 2, 2, [[1, -165, [17]]], [0, "74eJLfGhNEMopOxsgW7djm", 1, 0], [5, 19, 19], [0, 74.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "f23270.30", 2, 2, [[1, -166, [18]]], [0, "7fu4oOlxFMcbbv6OjLgm1g", 1, 0], [5, 19, 19], [0, 35.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "f23270.30", 2, 2, [[1, -167, [19]]], [0, "91nqi+ukNEYqhXXg0NZbwg", 1, 0], [5, 19, 19], [0, -3.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "f23270.30", 2, 2, [[1, -168, [20]]], [0, "b0q38KXwtCybukVsRb/T1y", 1, 0], [5, 19, 19], [0, -42.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "f23270.30", 2, 2, [[1, -169, [21]]], [0, "d7eW1P+4ZMvq6sv8bZyPTK", 1, 0], [5, 19, 19], [0, -81.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [6, "f23270.30", 1, 2, 2, [[1, -170, [22]]], [0, "8aSFqcIBxMII17tBD8Tkn8", 1, 0], [5, 19, 19], [0, -120.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [6, "f23270.30", 1, 2, 2, [[1, -171, [23]]], [0, "60I271jNNOOq0yvxguDD2g", 1, 0], [5, 19, 19], [0, -159.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [6, "f23270.30", 1, 2, 2, [[1, -172, [24]]], [0, "46EIDDI2dDP4EQpe+8MZpw", 1, 0], [5, 19, 19], [0, -198.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [6, "f23270.30", 1, 2, 2, [[1, -173, [25]]], [0, "f54uXeBg5HC5Uh6/PsYJtn", 1, 0], [5, 19, 19], [0, -237.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [6, "f23270.30", 1, 2, 2, [[1, -174, [26]]], [0, "8fRhBaFqtGU4prYKsGgCT7", 1, 0], [5, 19, 19], [0, -276.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [6, "f23270.30", 1, 2, 2, [[1, -175, [27]]], [0, "7eHcSJuWxD4IcQSJeO6sXP", 1, 0], [5, 19, 19], [0, -315.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [13, "1=imgIcon", 3, 7, [[1, -176, [28]]], [0, "22OgvlfqtIgoGczGpF6JK6", 1, 0], [5, 68, 68]], [14, "hecheng", 2, 3, [[11, "default", "animation", 0, false, "animation", -177, [41]]], [0, "f624dYsV9KH7jBgf3nAcba", 1, 0], [-776.721, 162.722, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "xia<PERSON><PERSON>", 2, 3, [[11, "default", "animation", 0, false, "animation", -178, [42]]], [0, "36uLutix5NnZdQ4JBuWtvv", 1, 0], [-833.484, -231.492, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "xx", 2, 3, [[11, "default", "animation", 0, false, "animation", -179, [43]]], [0, "e6vrMDixxCr7EgmdBht8UE", 1, 0], [5, 75.88, 120.62], [-833.484, -461.038, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "title", 2, 5, [[16, "难度规范", 1, 1, -180, [52]]], [0, "03o1PsmI9PrYUCnuCC58hK", 1, 0], [4, 4278190080], [5, 160, 50.4], [0, 630, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "adjustBtn", false, 2, 3, [[63, -181]], [0, "b4LVB4Fs1NPKzMKjgZBO8J", 1, 0], [5, 200, 80], [271.209, 502.696, 0, 0, 0, 0, 1, 1, 1, 1]], [47, 1, 0, 31, [53]], [52, 20, 25, false, 1, 1, 32], [53, "数值范围:10-120", 20, 25, false, 1, 1, 33, [54]], [35, "Label", 512, 2, 20, [[54, "确定", 20, false, 1, 1, 1, 1, -182, [55]]], [0, "23hIxa3P1ERqsD7wAO1SS9", 1, 0], [4, 4278190080], [5, 100, 40]], [36, "title", false, 2, 1, [[55, "合成葡萄", 46, 55, 1, 1, -183, [60]]], [0, "3dLoZ+XbJGLbm8wDtnmh6t", 1, 0], [4, 4278190080], [5, 184, 69.3], [0, 525, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 5, 1, 0, -1, 3, 0, -2, 68, 0, 0, 2, 0, -1, 38, 0, -2, 39, 0, -3, 40, 0, -4, 41, 0, -5, 42, 0, -6, 43, 0, -7, 44, 0, -8, 45, 0, -9, 46, 0, -10, 47, 0, -11, 48, 0, -12, 49, 0, -13, 50, 0, -14, 51, 0, -15, 52, 0, -16, 53, 0, -17, 54, 0, -18, 55, 0, -19, 56, 0, -20, 57, 0, -1, 35, 0, -2, 36, 0, -3, 22, 0, -4, 37, 0, -5, 23, 0, -6, 24, 0, -7, 26, 0, 2, 3, 0, -10, 21, 0, -11, 7, 0, -12, 4, 0, -13, 59, 0, -14, 60, 0, -15, 61, 0, 2, 3, 0, -17, 5, 0, 2, 3, 0, -19, 63, 0, -20, 6, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, -5, 12, 0, -6, 13, 0, -7, 14, 0, -8, 15, 0, -9, 16, 0, -10, 17, 0, -11, 18, 0, -12, 19, 0, 0, 5, 0, -1, 27, 0, -2, 28, 0, -3, 29, 0, -4, 30, 0, -5, 62, 0, 6, 64, 0, 7, 66, 0, 8, 65, 0, 0, 6, 0, -1, 31, 0, -2, 32, 0, -3, 33, 0, -4, 34, 0, 0, 7, 0, 0, 7, 0, 0, 7, 0, -1, 58, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, -1, 67, 0, 0, 21, 0, 0, 21, 0, 2, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, -1, 25, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, -1, 64, 0, 0, 31, 0, -1, 65, 0, 0, 32, 0, -1, 66, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 0, 59, 0, 0, 60, 0, 0, 61, 0, 0, 62, 0, 0, 63, 0, 0, 67, 0, 0, 68, 0, 9, 1, 2, 2, 3, 20, 2, 34, 183], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 64], [-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, 1, 3, 4, -1, 1], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 3, 3, 4, 0, 5]]