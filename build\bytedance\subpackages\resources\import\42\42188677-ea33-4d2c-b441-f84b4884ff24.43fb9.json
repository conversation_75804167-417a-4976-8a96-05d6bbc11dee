[1, ["ecpdLyjvZBwrvm+cedCcQy", "f8npR6F8ZIZoCp3cKXjJQz", "29FYIk+N1GYaeWH/q1NxQO", "a2MjXRFdtLlYQ5ouAFv/+R", "73jGpne/9JUI/171Zur5Pr", "c1f4UDWZJNUJmmvN1IN/rK", "f0BIwQ8D5Ml7nTNQbh1YlS", "f6NcoTBPNJ4qSyD40PNpRP", "40OtPssnZP9antMAWsDtDT", "e5AxFrgEJJZbqWIxtHFSNA", "ef1j2vS8ROabD/02fuTola", "f7293wEF9JhIuMEsrLuqng"], ["node", "_spriteFrame", "_N$file", "_N$disabledSprite", "_N$normalSprite", "root", "btnConfirm", "codeEditBox", "_N$target", "data", "_parent"], [["cc.Node", ["_name", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color"], 1, 4, 5, 9, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Label", ["_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_string", "_N$cacheMode", "_lineHeight", "_enableWrapText", "_N$overflow", "_spacingX", "_styleFlags", "node", "_materials", "_N$file"], -8, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_children", "_trs"], 1, 1, 2, 4, 5, 2, 7], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "_left", "_right", "_top", "_bottom", "node"], -5, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite", "_N$normalSprite"], 1, 1, 9, 5, 5, 1, 6, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.EditBox", ["_N$inputMode", "node", "_N$textLabel", "_N$placeholderLabel", "_N$background"], 2, 1, 1, 1, 1], ["4ca5fDMMNFBPY+Rnc0g/iTb", ["node", "labelArr", "codeEditBox", "btnConfirm"], 3, 1, 2, 1, 1]], [[5, 0, 1, 2, 2], [0, 0, 5, 6, 4, 2, 3, 7, 2], [0, 0, 5, 4, 2, 3, 7, 2], [3, 0, 2, 3, 4, 5, 2], [1, 3, 4, 5, 1], [10, 0, 1, 2, 2], [11, 0, 1, 2, 3], [7, 0, 2], [0, 0, 6, 4, 2, 3, 2], [0, 0, 1, 5, 4, 2, 8, 3, 3], [0, 0, 5, 6, 2, 3, 2], [0, 0, 5, 4, 2, 3, 2], [0, 0, 6, 4, 2, 3, 7, 2], [8, 0, 1, 2, 3, 4, 5, 2], [3, 0, 2, 6, 3, 4, 5, 7, 2], [3, 0, 1, 2, 3, 4, 5, 3], [1, 0, 3, 4, 5, 2], [1, 1, 0, 3, 4, 5, 3], [1, 2, 1, 0, 3, 4, 5, 4], [1, 1, 0, 3, 4, 3], [4, 0, 1, 2, 8, 4], [4, 3, 0, 4, 5, 6, 7, 1, 2, 8, 9], [5, 1, 2, 1], [9, 0, 1], [2, 4, 0, 1, 9, 10, 2, 3, 5, 11, 12, 9], [2, 0, 6, 7, 1, 2, 3, 8, 11, 12, 8], [2, 4, 0, 6, 7, 1, 2, 3, 8, 11, 12, 9], [2, 4, 0, 7, 1, 2, 3, 5, 11, 12, 13, 8], [2, 4, 0, 6, 1, 2, 3, 5, 11, 12, 13, 8], [6, 1, 0, 2, 3, 4, 5, 6, 8, 7, 3], [6, 0, 2, 3, 4, 5, 6, 7, 2], [12, 0, 1, 2, 3, 4, 2], [13, 0, 1, 2, 3, 1]], [[7, "ExchangeCodeView"], [8, "ExchangeCodeView", [-6, -7], [[32, -5, [-4], -3, -2]], [22, -1, 0], [5, 750, 1334]], [10, "bg", 1, [-8, -9, -10, -11, -12], [0, "f2Cua3yhlI6ZrQKo0+cZ/i", 1, 0], [5, 750, 1334]], [14, "enditBox", 2, [-14, -15, -16], [-13], [0, "b2GsO/YTBMrbyOJHYmFps2", 1, 0], [5, 300, 80], [0, 32.927, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "Background", [-19], [[4, -17, [16], 17], [21, 0, 45, 7, 7, 0.5, 0.5, 100, 40, -18]], [0, "cfWnqg1o1I17knBpJGxqlP", 1, 0], [5, 206, 80], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "titlebg", 2, [-21, -22], [[18, false, 1, 0, -20, [9], 10]], [0, "35swYcU8JANI0dCQ4kmXEL", 1, 0], [5, 520, 82], [0, 165.283, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnConfirm", 2, [4], [[30, 3, -23, [[6, "4ca5fDMMNFBPY+Rnc0g/iTb", "onClickConfirm", 1]], [4, 4293322470], [4, 3363338360], 4, 18]], [0, "a2QUrX2+BGJpJ+EXO5rczO", 1, 0], [5, 220, 81], [0, -91.601, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "maskbg", 190, 1, [[16, 0, -24, [0], 1], [20, 45, 750, 1334, -25]], [0, "d43JuPMF5Oo6DkR4RaHTW8", 1, 0], [4, 4281542699], [5, 750, 1334]], [11, "imgbg", 2, [[17, 1, 0, -26, [2], 3], [23, -27]], [0, "f6ri3/00pMCrhxsYSHRV+N", 1, 0], [5, 520, 340]], [13, "title", 5, [[-28, [5, 2, -29, [4, 4278190080]]], 1, 4], [0, "d3aBNZ6hlD0LlsJWBT33m+", 1, 0], [5, 148, 54.4], [0, -22.887, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnClose", 5, [-32], [[29, 0.9, 3, -31, [[6, "4ca5fDMMNFBPY+Rnc0g/iTb", "onClickFrame", 1]], [4, 4293322470], [4, 3363338360], -30, 7, 8]], [0, "3dfj+kkSBG9J2F18fVEzy9", 1, 0], [5, 62, 65], [206.736, -30.245, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Background", 10, [[4, -33, [5], 6]], [0, "384uMkOUxEOpXIyrMlWpmx", 1, 0], [5, 64, 65], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "txt", 4, [[27, "确 认", 36, false, false, 1, 1, 1, -34, [14], 15], [5, 2, -35, [4, 4278190080]]], [0, "f1Ifh6m/JD+YVgDQiK0Biq", 1, 0], [5, 84.78, 54.4], [0, 5.799, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "兑换码", 48, false, 1, 1, 1, 1, 1, 9, [4]], [3, "img_bg", 3, [-36], [0, "1eeKA3h69Dp7wrMrb9A6d6", 1, 0], [5, 396, 80]], [19, 1, 0, 14, [11]], [15, "TEXT_LABEL", false, 3, [-37], [0, "61SdlCoGxGtqD73DRCdejm", 1, 0], [5, 300, 50.4]], [25, 30, 30, false, false, 1, 1, 1, 16, [12]], [3, "PLACEHOLDER_LABEL", 3, [-38], [0, "cc3ktbEZVKcqpiYxXFFZYL", 1, 0], [5, 372, 37.8]], [26, "输入兑换码", 30, 30, false, false, 1, 1, 2, 18, [13]], [31, 6, 3, 17, 19, 15], [2, "tips", 2, [[28, "点击空白区域可关闭", 30, 30, false, 1, 1, 1, -39, [19], 20]], [0, "37sF/eLC9NjrJ192ArUPMZ", 1, 0], [5, 270, 37.8], [0, -219.925, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 5, 1, 0, 6, 6, 0, 7, 20, 0, -1, 13, 0, 0, 1, 0, -1, 7, 0, -2, 2, 0, -1, 8, 0, -2, 5, 0, -3, 3, 0, -4, 6, 0, -5, 21, 0, -1, 20, 0, -1, 14, 0, -2, 16, 0, -3, 18, 0, 0, 4, 0, 0, 4, 0, -1, 12, 0, 0, 5, 0, -1, 9, 0, -2, 10, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, -1, 13, 0, 0, 9, 0, 8, 11, 0, 0, 10, 0, -1, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, -1, 15, 0, -1, 17, 0, -1, 19, 0, 0, 21, 0, 9, 1, 4, 10, 6, 39], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 15, 17, 19], [-1, 1, -1, 1, -1, -1, 1, 4, 3, -1, 1, -1, -1, -1, -1, 2, -1, 1, 3, -1, 2, 2, 1, 2, 2], [0, 3, 0, 4, 0, 0, 5, 6, 2, 0, 7, 0, 0, 0, 0, 1, 0, 8, 2, 0, 1, 1, 9, 10, 11]]