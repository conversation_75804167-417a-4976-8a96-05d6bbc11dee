[1, ["ecpdLyjvZBwrvm+cedCcQy", "07uTi5pw5INIAU5mtPfWfY", "e4TMDxF3lEk7wOCbX7r9mN", "a2MjXRFdtLlYQ5ouAFv/+R", "ef1j2vS8ROabD/02fuTola", "0bNy06XqNOgKsDrtBlOZTu", "7a/QZLET9IDreTiBfRn2PD"], ["node", "_spriteFrame", "_N$file", "root", "data", "_parent"], [["cc.Node", ["_name", "_opacity", "_groupIndex", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_color"], -1, 4, 5, 9, 1, 2, 7, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "alignMode", "_originalHeight", "_bottom", "_top", "node"], -3, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_color", "_trs"], 2, 1, 12, 4, 5, 5, 7], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_isSystemFontUsed", "_N$cacheMode", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "timeScale", "_animationName", "node", "_materials"], -2, 1, 3], ["b5c4fM0/uNGW5m2jINFxD1t", ["AmType", "node"], 2, 1], ["3157fep46xMPaZM4v+BUk7T", ["UItype", "node", "labelArr"], 2, 1, 2]], [[4, 0, 1, 2, 2], [3, 1, 0, 2, 3, 4, 3], [7, 0, 1, 2, 2], [5, 0, 1, 2, 3, 4, 5, 9, 10, 7], [6, 0, 2], [0, 0, 2, 8, 6, 4, 5, 3], [0, 0, 3, 7, 8, 6, 4, 10, 5, 3], [0, 0, 1, 7, 6, 4, 10, 5, 3], [0, 0, 1, 7, 6, 4, 5, 9, 3], [0, 0, 1, 7, 6, 4, 10, 5, 9, 3], [0, 0, 7, 8, 6, 4, 5, 2], [0, 0, 8, 4, 5, 2], [0, 0, 7, 6, 4, 10, 5, 9, 2], [0, 0, 7, 8, 6, 4, 5, 9, 2], [0, 0, 7, 6, 4, 5, 9, 2], [2, 0, 1, 2, 3, 4, 2], [2, 0, 1, 2, 3, 5, 4, 6, 2], [3, 0, 2, 3, 4, 2], [1, 2, 0, 1, 3, 6, 5], [1, 2, 0, 4, 1, 6, 5], [1, 2, 0, 5, 1, 6, 5], [1, 0, 6, 2], [1, 0, 1, 3, 6, 4], [4, 1, 2, 1], [5, 0, 1, 2, 6, 7, 3, 4, 5, 8, 9, 10, 11, 10], [8, 0, 1, 2, 3, 4, 5, 6, 6], [9, 0, 1, 2], [10, 0, 1, 2, 2]], [[4, "<PERSON><PERSON><PERSON><PERSON>iew"], [5, "<PERSON><PERSON><PERSON><PERSON>iew", 1, [-5, -6], [[27, 0, -4, [-2, -3]]], [23, -1, 0], [5, 750, 1334]], [6, "maskbg", 512, 1, [-9, -10, -11], [[21, 45, -7], [17, 0, -8, [6], 7]], [0, "aapd7EisVH5IMSjQhyw9DD", 1, 0], [4, 4281674542], [5, 750, 1334]], [11, "content", [-12, -13, -14, -15], [0, "25PBU9f2ZJZ4/u95EdX6c9", 1, 0], [5, 679, 1126]], [7, "img_long", 10, 2, [[1, 2, 0, -16, [0], 1], [18, 0, 45, 750, 1624, -17]], [0, "00aHNBq7ZHLJ78zOmrBDsG", 1, 0], [4, 4278190080], [5, 750, 1334]], [8, "img_dian", 39, 2, [[1, 2, 0, -18, [2], 3], [19, 0, 44, 2, 750, -19]], [0, "14+Vf1NS5FqYv0gwot0HmW", 1, 0], [5, 750, 320], [0, -505, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "img_dian", 30, 2, [[1, 2, 0, -20, [4], 5], [20, 0, 41, 2, 750, -21]], [0, "04xgT+vqhBGLXhPhGUxKA6", 1, 0], [4, 4278190080], [5, 750, 320], [0, 505, 0, 0, 0, 0, 1, 1, -1, 1]], [10, "bg", 1, [3], [[22, 45, 750, 1334, -22]], [0, "4fWF+uzolAhpRXn19R4QOR", 1, 0], [5, 750, 1334]], [12, "Label", 3, [[24, "点击空白继续", 30, 32, false, false, 1, 1, 1, 1, -23, [8], 9], [2, 4, -24, [4, 3573547008]]], [0, "fdJYYGS89B96h2JTQGYsOS", 1, 0], [4, 4290032820], [5, 188, 48.32], [0, -370, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "img_qz03", 3, [-26], [[1, 1, 0, -25, [11], 12]], [0, "6flsFKOidKELtyJuh8wdbb", 1, 0], [5, 163, 42], [0, 313.968, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "tap", 9, [[-27, [2, 2, -28, [4, 4278190080]]], 1, 4], [0, "57ZtVk3/RMrakUS2QPDla2", 1, 0], [5, 54, 35.5]], [16, "name", 3, [[-29, [2, 3, -30, [4, 4278190080]]], 1, 4], [0, "e75SKMjHJKYL4BMB0Jzy1p", 1, 0], [4, 4294957639], [5, 118, 81.6], [0, 259.37, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "ske", 3, [[25, "default", "attack2", 0, 0.6, "attack2", -31, [14]], [26, 2, -32]], [0, "98qlVU1SNMIZtJENEp9xvh", 1, 0], [5, 158.35684204101562, 160.63531494140625], [0, -123.02, 0, 0, 0, 0, 1, 2, 2, 1]], [3, "普通", 25, 25, 1, 1, 1, 10, [10]], [3, "灵猫", 56, 60, 1, 1, 1, 11, [13]]], 0, [0, 3, 1, 0, -1, 14, 0, -2, 13, 0, 0, 1, 0, -1, 2, 0, -2, 7, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -1, 8, 0, -2, 9, 0, -3, 11, 0, -4, 12, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -1, 10, 0, -1, 13, 0, 0, 10, 0, -1, 14, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 4, 1, 3, 5, 7, 32], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, -1, 1, -1, -1], [0, 2, 0, 1, 0, 1, 0, 3, 0, 4, 0, 0, 5, 0, 6]]