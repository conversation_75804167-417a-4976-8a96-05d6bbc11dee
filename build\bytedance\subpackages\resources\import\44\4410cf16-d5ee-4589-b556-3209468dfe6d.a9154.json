[1, ["ecpdLyjvZBwrvm+cedCcQy", "f5YmCCSe1OrIVUT+Qote1i", "f7293wEF9JhIuMEsrLuqng"], ["node", "_spriteFrame", "_N$file", "root", "label", "data"], [["cc.Node", ["_name", "_groupIndex", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs"], 1, 9, 4, 5, 2, 1, 7], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_groupIndex", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 1, 1, 2, 4, 5, 7], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$fontFamily", "node", "_materials"], -4, 1, 3], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$paddingRight", "node", "_layoutSize"], -1, 1, 5], ["a8c02IQhPdEI6RNnarHRhOQ", ["node", "label"], 3, 1, 1]], [[2, 0, 1, 2, 2], [3, 0, 2], [0, 0, 1, 5, 2, 3, 4, 3], [0, 0, 1, 6, 5, 2, 3, 4, 3], [0, 0, 1, 6, 2, 3, 4, 7, 3], [4, 0, 1, 2, 3, 4, 5, 6, 3], [1, 0, 2, 3, 2], [1, 1, 0, 2, 3, 4, 3], [2, 1, 2, 1], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 8], [6, 0, 1, 2, 3, 4, 5, 5], [7, 0, 1, 1]], [[1, "tip"], [2, "tip", 1, [-4], [[11, -3, -2]], [8, -1, 0], [5, 77, 78]], [3, "bg", 1, 1, [-7, -8], [[7, 1, 0, -5, [2], 3], [10, 1, 1, 20, 20, -6, [5, 189.95, 70]]], [0, "97tCGQ035C45yZBn3anPwr", 1, 0], [5, 189.95, 70]], [4, "icon", 1, 2, [[6, 2, -9, [0]]], [0, "2eXBsVaMJDuqXTAJbRwDLi", 1, 0], [5, 62, 65], [-43.974999999999994, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [5, "tipsText", 1, 2, [-10], [0, "8e1c4OUKRHuIcUAKTjwmnJ", 1, 0], [5, 87.95, 45.36], [31.000000000000007, 1.358, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "+1000", 30, 36, false, 1, 1, "微软雅黑", 4, [1]]], 0, [0, 3, 1, 0, 4, 5, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, 0, 3, 0, -1, 5, 0, 5, 1, 10], [0, 0, 0, 0, 5], [-1, -1, -1, 1, 2], [0, 0, 0, 1, 2]]