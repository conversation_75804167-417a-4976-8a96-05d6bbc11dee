[1, ["ecpdLyjvZBwrvm+cedCcQy", "ef1j2vS8ROabD/02fuTola", "7d1EjwRqBJh7jheADVzsCF", "29FYIk+N1GYaeWH/q1NxQO", "a2MjXRFdtLlYQ5ouAFv/+R", "c3+ruR7+FEnKfu8yo+WDeT", "29R34cGbFB7YQJIiPE06Fl", "1daaY1VXJHTZOf6YmkFtkv", "86IEClttZOcbBlnDuaCb28"], ["node", "_spriteFrame", "_N$file", "_parent", "_N$normalSprite", "_N$disabledSprite", "_N$font", "root", "richText", "data"], [["cc.Node", ["_name", "_groupIndex", "_opacity", "_prefab", "_contentSize", "_components", "_children", "_parent", "_trs", "_color"], 0, 4, 5, 9, 2, 1, 7, 5], ["cc.Layout", ["_N$layoutType", "_resize", "_N$spacingY", "_N$paddingBottom", "_N$paddingLeft", "_N$spacingX", "_N$paddingTop", "_N$affectedByScale", "_enabled", "node", "_layoutSize"], -6, 1, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "_fontSize", "_enableWrapText", "_N$overflow", "_N$cacheMode", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize"], 2, 1, 12, 4, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$horizontalAlign", "_N$fontSize", "_N$maxWidth", "node"], -2, 1], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$normalSprite", "_N$disabledSprite"], 1, 1, 9, 5, 5, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["d7343ixS71CLp2uB53OhA0o", ["node", "nodeArr", "labelArr", "richText"], 3, 1, 2, 2, 1]], [[4, 0, 1, 2, 2], [2, 1, 0, 2, 3, 4, 3], [0, 0, 7, 5, 3, 4, 8, 2], [9, 0, 1, 2, 2], [0, 0, 7, 6, 5, 3, 4, 8, 2], [0, 0, 6, 5, 3, 4, 8, 2], [3, 0, 1, 2, 3, 4], [5, 0, 5, 6, 1, 2, 3, 7, 8, 9, 10, 11, 9], [11, 0, 1, 2, 3, 4, 5, 6, 7, 3], [12, 0, 1, 2, 3], [6, 0, 2], [0, 0, 1, 6, 5, 3, 4, 3], [0, 0, 2, 7, 5, 3, 9, 4, 3], [0, 0, 7, 6, 5, 3, 4, 2], [0, 0, 6, 5, 3, 4, 2], [0, 0, 7, 6, 3, 4, 8, 2], [7, 0, 1, 2, 3, 4, 2], [8, 0, 1, 2, 3, 4, 5, 2], [2, 0, 2, 3, 4, 2], [3, 0, 3, 2], [4, 1, 2, 1], [5, 0, 1, 4, 2, 3, 9, 10, 6], [1, 1, 0, 4, 5, 2, 9, 10, 6], [1, 1, 0, 6, 3, 2, 7, 9, 10, 7], [1, 8, 0, 9, 10, 3], [1, 1, 0, 3, 2, 9, 10, 5], [10, 0, 1, 2, 3, 4, 5, 6], [13, 0, 1], [14, 0, 1, 2, 3, 1]], [[10, "Item_ListConfirm"], [11, "Item_ListConfirm", 1, [-6, -7], [[28, -5, [-4], [-3], -2]], [20, -1, 0], [5, 750, 1335]], [14, "content", [-11, -12, -13], [[1, 1, 0, -8, [17], 18], [27, -9], [25, 1, 2, 30, 10, -10, [5, 600, 456.4]]], [0, "3fBLGLK/9I64VoqeVraltV", 1, 0], [5, 600, 456.4]], [4, "box", 2, [-16, -17], [[23, 1, 2, 20, 20, 20, true, -14, [5, 560, 230.4]], [1, 1, 0, -15, [3], 4]], [0, "ccEn+zzZdJhLLkCtV3N77a", 1, 0], [5, 560, 230.4], [0, 16.999999999999986, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btn_coutinue", [-21], [[8, 0.9, 3, -18, [[9, "d7343ixS71CLp2uB53OhA0o", "onClickConfirm", 1]], [4, 4293322470], [4, 3363338360], 13, 14], [1, 1, 0, -19, [15], 16], [24, false, 1, -20, [5, 183, 90]]], [0, "5fGKRdpPNCYa8Ir/LcM6jW", 1, 0], [5, 180, 90], [116.19, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "btn_coutinue", [-24], [[8, 0.9, 3, -22, [[9, "d7343ixS71CLp2uB53OhA0o", "onClickClose", 1]], [4, 4293322470], [4, 3363338360], 7, 8], [1, 1, 0, -23, [9], 10]], [0, "cbrPcPLhtAsZY497T29/qv", 1, 0], [5, 180, 90], [-114.285, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [12, "mask", 177.98999999999998, 1, [[18, 0, -25, [0], 1], [6, 45, 750, 1334, -26]], [0, "cbRl9tYEpNXrr745oO8H7O", 1, 0], [4, 4281542699], [5, 750, 1335]], [13, "bg", 1, [2], [[6, 45, 750, 1334, -27]], [0, "0fUFUTX/FJ9oWQdPB+OXtu", 1, 0], [5, 750, 1335]], [4, "title_zhua<PERSON><PERSON>", 2, [-29], [[19, 1, -28]], [0, "b5RklW5wtFCoeQEs7JmIZC", 1, 0], [5, 488, 86], [0, 185.2, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "Label_title", 8, [[-30, [3, 4, -31, [4, 4278190080]]], 1, 4], [0, "f0YAxpgTVEL5P9vFDZFbvd", 1, 0], [5, 88, 58.4]], [2, "rewardList", 3, [[22, 1, 1, 5, 15, 15, -32, [5, 520, 120]]], [0, "91yc0nRF1KMLY9um96yMfd", 1, 0], [5, 520, 120], [0, 35.2, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "New Node", 2, [5, 4], [0, "1c2IkOBOtEkac9qP8ydIVq", 1, 0], [5, 488, 90], [0, -153.20000000000002, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 5, [[7, "关闭", 30, false, false, 1, 1, 2, 1, -33, [5], 6], [3, 4, -34, [4, 4284756992]]], [0, "bdYDKJX69O5Zsx4/ahX+9C", 1, 0], [5, 139, 58.4], [0, 3.08, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 4, [[7, "确认", 30, false, false, 1, 1, 2, 1, -35, [11], 12], [3, 4, -36, [4, 4278732178]]], [0, "a1EH3TFARF8o2VAxtlSpSL", 1, 0], [5, 200, 58.4], [0, 3.08, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "提示", false, 1, 1, 1, 9, [2]], [17, "Label_tips", 3, [-37], [0, "654qLQYVdHYZTKmGce0Qva", 1, 0], [5, 400, 50.4], [0, -70, 0, 0, 0, 0, 1, 1, 1, 1]], [26, false, "<outline color=#474747 width=3>之前有未完成的战斗，是否继续？</outline>", 1, 26, 400, 15]], 0, [0, 7, 1, 0, 8, 16, 0, -1, 14, 0, -1, 10, 0, 0, 1, 0, -1, 6, 0, -2, 7, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 8, 0, -2, 3, 0, -3, 11, 0, 0, 3, 0, 0, 3, 0, -1, 10, 0, -2, 15, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 13, 0, 0, 5, 0, 0, 5, 0, -1, 12, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, -1, 9, 0, -1, 14, 0, 0, 9, 0, 0, 10, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, -1, 16, 0, 9, 1, 2, 3, 7, 4, 3, 11, 5, 3, 11, 37], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 16], [-1, 1, -1, -1, 1, -1, 2, 4, 5, -1, 1, -1, 2, 4, 5, -1, 1, -1, 1, 2, 6], [0, 4, 0, 0, 5, 0, 1, 2, 3, 0, 6, 0, 1, 2, 3, 0, 7, 0, 8, 1, 1]]