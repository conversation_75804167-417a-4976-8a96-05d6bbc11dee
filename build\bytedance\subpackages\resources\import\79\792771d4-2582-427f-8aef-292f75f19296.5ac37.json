[1, ["ecpdLyjvZBwrvm+cedCcQy", "ef1j2vS8ROabD/02fuTola", "a2MjXRFdtLlYQ5ouAFv/+R", "22cjIamqZH/Lbdvp80pFLv", "65OUoZ25pGHqftEDT5VTS4", "29FYIk+N1GYaeWH/q1NxQO", "f6NcoTBPNJ4qSyD40PNpRP", "23Tw89umhO5pVi85QDxfi5", "81hvG9OTBFCZJRG7wuUxUF"], ["node", "_spriteFrame", "_N$file", "_parent", "_N$disabledSprite", "templete", "root", "my<PERSON>rid<PERSON>iew", "_N$target", "_N$content", "data"], [["cc.Node", ["_name", "_opacity", "_groupIndex", "_active", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 5, 9, 1, 7, 2, 5, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "alignMode", "_originalHeight", "node"], -1, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_styleFlags", "_enableWrapText", "_N$cacheMode", "_fontSize", "_lineHeight", "node", "_materials", "_N$file"], -7, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalColor", "_N$disabledColor", "_N$target", "_N$disabledSprite"], 2, 1, 9, 5, 5, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["9bd0fvydv5K0ZXnwdO5A2Td", ["node", "space", "content"], 3, 1, 5, 1], ["2047bLpfEJAB6XQC/rOXZ6+", ["node", "my<PERSON>rid<PERSON>iew"], 3, 1, 1]], [[4, 0, 1, 2, 2], [2, 1, 0, 2, 3, 4, 3], [0, 0, 7, 6, 4, 5, 8, 2], [1, 0, 1, 3, 4, 4], [7, 0, 1, 2, 2], [5, 0, 2], [0, 0, 2, 9, 6, 4, 5, 3], [0, 0, 1, 7, 6, 4, 10, 5, 3], [0, 0, 7, 9, 6, 4, 5, 2], [0, 0, 9, 4, 5, 2], [0, 0, 7, 6, 4, 5, 2], [0, 0, 7, 9, 6, 4, 5, 8, 2], [0, 0, 9, 6, 4, 5, 8, 2], [0, 0, 3, 7, 6, 4, 10, 5, 8, 3], [0, 0, 1, 7, 6, 4, 10, 5, 8, 3], [0, 0, 7, 9, 6, 4, 5, 11, 8, 2], [0, 0, 7, 6, 4, 5, 11, 8, 2], [6, 0, 1, 2, 3, 4, 5, 6, 2], [1, 0, 4, 2], [1, 2, 0, 1, 4, 4], [2, 0, 2, 3, 4, 2], [2, 1, 2, 3, 4, 2], [4, 1, 2, 1], [3, 0, 1, 5, 2, 3, 4, 10, 11, 12, 7], [3, 0, 6, 1, 2, 3, 4, 7, 10, 11, 12, 8], [3, 0, 8, 9, 1, 5, 2, 3, 4, 10, 11, 12, 9], [8, 0, 1, 2, 3, 4, 5, 6, 2], [9, 0, 1, 2, 3], [10, 0, 1, 2, 2], [11, 0, 1, 2, 3, 4, 5, 6, 6], [12, 0, 1, 2, 1], [13, 0, 1, 1]], [[5, "PetAttrListView"], [6, "PetAttrListView", 1, [-4, -5], [[31, -3, -2]], [22, -1, 0], [5, 750, 1334]], [9, "content", [-6, -7, -8, -9, -10], [0, "a0g5188p1HII/Ci8AK/QxX", 1, 0], [5, 680, 914]], [12, "Background", [-14], [[21, 1, -11, [8], 9], [26, 3, -13, [[27, "2047bLpfEJAB6XQC/rOXZ6+", "close", 1]], [4, 4293322470], [4, 3363338360], -12, 10]], [0, "90OlI7eu5N6YuKmHM/sieV", 1, 0], [5, 52, 56], [269.386, -0.668, 0, 0, 0, 0, 1, 1, 1, 0]], [11, "title_zhua<PERSON><PERSON>", 2, [-16, 3], [[1, 1, 0, -15, [11], 12]], [0, "45NuY+WQ9L5J6TOpjXxAQa", 1, 0], [5, 680, 82], [0, 423.977, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "New ScrollView", 2, [-20], [[[29, false, 0.75, 0.23, null, null, -18, -17], -19], 4, 1], [0, "c16wooUcRFu7atWQTNsgll", 1, 0], [5, 620, 700], [0, -2.954, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "view", 5, [-23], [[28, 0, -21, [17]], [3, 45, 240, 250, -22]], [0, "f6rJOdci5LpqR2itdXzuYz", 1, 0], [5, 620, 700], [0, 0.5, 1], [0, 350, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "content", 6, [[19, 0, 41, 614, -24]], [0, "ecKprdHUBMRoHqCj7C6P4V", 1, 0], [5, 620, 274], [0, 0, 1], [-310, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "maskbg", 200, 1, [[18, 45, -25], [20, 0, -26, [0], 1]], [0, "7fJDaj8qtAD4iL63ze9MUa", 1, 0], [4, 4278190080], [5, 750, 1334]], [8, "bg", 1, [2], [[3, 45, 750, 1334, -27]], [0, "c6b+DUrApP2JB86Z7Ioh5n", 1, 0], [5, 750, 1334]], [2, "Label_title", 4, [[23, "详情预览", false, 1, 1, 1, 2, -28, [4], 5], [4, 4, -29, [4, 4278190080]]], [0, "6aRvpyzjtLSqbRuVrwAnNW", 1, 0], [5, 285, 56.4], [0, -0.224, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label_title", 2, [[25, "拥有宠物，升级获得效果", 30, 30, false, 1, 1, 1, 2, -30, [15], 16], [4, 4, -31, [4, 4278190080]]], [0, "d8Xl58imhMlZrHmdkhNjnu", 1, 0], [5, 400, 56.4], [0, -403.258, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bg", 2, [[1, 1, 0, -32, [2], 3]], [0, "32XWbHc3VCJoxAH6kEEwjT", 1, 0], [5, 680, 914]], [13, "Label", false, 3, [[24, "返回", false, false, 1, 1, 1, 1, -33, [6], 7]], [0, "32xj1jPPxLkrFw1riXxUhd", 1, 0], [4, 4278209897], [5, 100, 62], [0, 2.279, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "fkbg_02", 83, 2, [[1, 1, 0, -34, [13], 14]], [0, "ba3dgY0OJGsr6W/o7C1onV", 1, 0], [4, 4294962402], [5, 640, 720], [0, -4.65, 0, 0, 0, 0, 1, 1, 1, 1]], [30, 5, [0, 0, 20], 7]], 0, [0, 6, 1, 0, 7, 15, 0, 0, 1, 0, -1, 8, 0, -2, 9, 0, -1, 12, 0, -2, 4, 0, -3, 14, 0, -4, 11, 0, -5, 5, 0, 0, 3, 0, 8, 3, 0, 0, 3, 0, -1, 13, 0, 0, 4, 0, -1, 10, 0, 9, 7, 0, 0, 5, 0, -2, 15, 0, -1, 6, 0, 0, 6, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 10, 1, 2, 3, 9, 3, 3, 4, 34], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15], [-1, 1, -1, 1, -1, 2, -1, 2, -1, 1, 4, -1, 1, -1, 1, -1, 2, -1, 5], [0, 2, 0, 3, 0, 1, 0, 1, 0, 4, 5, 0, 6, 0, 7, 0, 1, 0, 8]]